<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
    width="60%"
    align-center
    :close-on-click-modal="false"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="用户名" prop="username">
            <ElInput v-model="formData.username" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="手机号" prop="phone">
            <ElInput v-model="formData.phone" />
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="20">
        <ElCol :span="24">
          <ElFormItem label="角色" prop="role">
            <ElSelect v-model="formData.role" @change="handleRoleChange" style="width: 100%">
              <ElOption
                v-for="role in roleOptions"
                :key="role.value"
                :value="role.value"
                :label="role.label"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 会员专用字段 -->
      <template v-if="formData.role === 'MEMBER'">
        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="负责助理" prop="managedById">
              <ElSelect v-model="formData.managedById" placeholder="请选择负责助理（可选）" clearable style="width: 100%">
                <ElOption
                  v-for="assistant in assistantList"
                  :key="assistant.id"
                  :value="assistant.id"
                  :label="`${assistant.username} (${assistant.phone || ''})`"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <!-- 会员基础信息 -->
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="英文名" prop="memberProfile.english_name">
              <ElInput v-model="formData.memberProfile.english_name" placeholder="用于FBA发票" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="产品所在仓库" prop="memberProfile.warehouse_location">
              <ElInput v-model="formData.memberProfile.warehouse_location" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="状态" prop="memberProfile.status">
              <ElSelect v-model="formData.memberProfile.status" style="width: 100%">
                <ElOption value="active" label="激活" />
                <ElOption value="inactive" label="未激活" />
                <ElOption value="suspended" label="暂停" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="说明" prop="memberProfile.description">
              <ElInput v-model="formData.memberProfile.description" placeholder="可填写地区等其它信息" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <!-- 财务相关 -->
        <ElDivider content-position="left">财务信息</ElDivider>
        <ElFormItem label="财务AuthToken" prop="memberProfile.finance_auth_token">
          <ElInput v-model="formData.memberProfile.finance_auth_token" type="password" show-password />
        </ElFormItem>
        <ElFormItem label="SKU前缀" prop="memberProfile.sku_prefix">
          <ElInput v-model="formData.memberProfile.sku_prefix" />
        </ElFormItem>
        <ElFormItem label="支付渠道" prop="memberProfile.payment_channel">
          <ElInput v-model="formData.memberProfile.payment_channel" />
        </ElFormItem>
        <ElFormItem label="父账号" prop="memberProfile.parent_account">
          <ElInput v-model="formData.memberProfile.parent_account" />
        </ElFormItem>

        <!-- EMS相关 -->
        <ElDivider content-position="left">EMS信息</ElDivider>
        <ElFormItem label="EMS大客户号" prop="memberProfile.ems_vip_number">
          <ElInput v-model="formData.memberProfile.ems_vip_number" />
        </ElFormItem>
        <ElFormItem label="EMS客户代码" prop="memberProfile.ems_customer_code">
          <ElInput v-model="formData.memberProfile.ems_customer_code" />
        </ElFormItem>
        <ElFormItem label="EMS版本信息" prop="memberProfile.ems_version_info">
          <ElInput v-model="formData.memberProfile.ems_version_info" />
        </ElFormItem>
        <ElFormItem label="international_eub_us_1.1" prop="memberProfile.international_eub_us">
          <ElInput v-model="formData.memberProfile.international_eub_us" />
        </ElFormItem>
        <ElFormItem label="EMS AuthToken" prop="memberProfile.ems_auth_token">
          <ElInput v-model="formData.memberProfile.ems_auth_token" type="password" show-password />
        </ElFormItem>

        <!-- 亚马逊相关 -->
        <ElDivider content-position="left">亚马逊信息</ElDivider>
        <ElFormItem label="亚马逊用户名" prop="memberProfile.amazon_username">
          <ElInput v-model="formData.memberProfile.amazon_username" />
        </ElFormItem>
        <ElFormItem label="亚马逊Seller ID" prop="memberProfile.amazon_seller_id">
          <ElInput v-model="formData.memberProfile.amazon_seller_id" />
        </ElFormItem>
        <ElFormItem label="亚马逊Marketplace ID" prop="memberProfile.amazon_marketplace_id">
          <ElInput v-model="formData.memberProfile.amazon_marketplace_id" />
        </ElFormItem>

        <!-- AWS相关 -->
        <ElDivider content-position="left">AWS信息</ElDivider>
        <ElFormItem label="AWS Access Key ID" prop="memberProfile.aws_access_key_id">
          <ElInput v-model="formData.memberProfile.aws_access_key_id" type="password" show-password />
        </ElFormItem>
        <ElFormItem label="Secret Key" prop="memberProfile.aws_secret_key">
          <ElInput v-model="formData.memberProfile.aws_secret_key" type="password" show-password />
        </ElFormItem>
        <ElFormItem label="Merchant Token" prop="memberProfile.merchant_token">
          <ElInput v-model="formData.memberProfile.merchant_token" type="password" show-password />
        </ElFormItem>
        <ElFormItem label="开发者AWS Access" prop="memberProfile.developer_aws_access">
          <ElInput v-model="formData.memberProfile.developer_aws_access" type="password" show-password />
        </ElFormItem>
        <ElFormItem label="开发者Secret Key" prop="memberProfile.developer_secret_key">
          <ElInput v-model="formData.memberProfile.developer_secret_key" type="password" show-password />
        </ElFormItem>
        <ElFormItem label="MWS授权令牌" prop="memberProfile.mws_auth_token">
          <ElInput v-model="formData.memberProfile.mws_auth_token" type="password" show-password />
        </ElFormItem>

        <!-- 业务相关 -->
        <ElDivider content-position="left">业务信息</ElDivider>
        <ElFormItem label="产品所在仓库" prop="memberProfile.warehouse_location">
          <ElInput v-model="formData.memberProfile.warehouse_location" />
        </ElFormItem>
        <ElFormItem label="状态" prop="memberProfile.status">
          <ElSelect v-model="formData.memberProfile.status">
            <ElOption value="active" label="激活" />
            <ElOption value="inactive" label="未激活" />
            <ElOption value="suspended" label="暂停" />
          </ElSelect>
        </ElFormItem>
      </template>

      <!-- 助理专用字段 -->
      <template v-if="isAssistantRole(formData.role)">
        <ElFormItem label="负责会员" prop="responsibleMemberIds">
          <ElSelect
            v-model="formData.responsibleMemberIds"
            multiple
            placeholder="请选择负责的会员（可选）"
            clearable
            style="width: 100%"
          >
            <ElOption
              v-for="member in memberList"
              :key="member.id"
              :value="member.id"
              :label="`${member.username} (${member.email})`"
            />
          </ElSelect>
        </ElFormItem>

        <ElFormItem label="说明" prop="assistantProfile.description">
          <ElInput v-model="formData.assistantProfile.description" type="textarea" placeholder="可填写地区等其它信息" />
        </ElFormItem>
        <ElFormItem label="关联财务助理AuthToken" prop="assistantProfile.related_finance_auth_token">
          <ElInput v-model="formData.assistantProfile.related_finance_auth_token" type="password" show-password />
        </ElFormItem>
        <ElFormItem v-if="formData.role === 'MEMBER_ASSISTANT'" label="内部会员财务AuthToken" prop="assistantProfile.internal_member_finance_auth_token">
          <ElInput v-model="formData.assistantProfile.internal_member_finance_auth_token" type="password" show-password placeholder="内部会员的助理才有此项" />
        </ElFormItem>
      </template>

      <!-- 权限选择 -->
      <ElFormItem v-if="shouldShowPermissions" label="用户权限" prop="permissionIds">
        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px;">
          <div v-for="(permissions, category) in permissionCategories" :key="category" style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #409eff;">{{ category }}</div>
            <ElCheckboxGroup v-model="formData.permissionIds">
              <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                <ElCheckbox
                  v-for="permission in permissions"
                  :key="permission.id"
                  :value="permission.id"
                  style="margin: 0;"
                >
                  {{ permission.name }}
                </ElCheckbox>
              </div>
            </ElCheckboxGroup>
          </div>
        </div>
      </ElFormItem>

      <ElFormItem v-if="dialogType === 'add'" label="初始密码" prop="password">
        <ElInput v-model="formData.password" type="password" placeholder="留空则使用默认密码" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">提交</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { UserService } from '@/api/usersApi'
import type { FormInstance, FormRules } from 'element-plus'
import { ElCheckbox, ElCheckboxGroup, ElDivider, ElMessage } from 'element-plus'

  interface Props {
    visible: boolean
    type: string
    userData?: any
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 角色选项
  const roleOptions = ref([
    { value: 'MEMBER', label: '普通会员' },
    { value: 'MEMBER_ASSISTANT', label: '会员助理' },
    { value: 'TRANSLATOR_ASSISTANT', label: '翻译助理' },
    { value: 'DOCUMENT_ASSISTANT', label: '文档助理' },
    { value: 'OPERATION_SPECIALIST', label: '精细化操作人员' },
    { value: 'FINANCE_STAFF', label: '财务人员' },
    { value: 'WAREHOUSE_MANAGER', label: '库管' },
    { value: 'REGION_ADMIN', label: '区域管理员' },
    { value: 'SUPER_ADMIN', label: '超级管理员' }
  ])

  // 对话框显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogType = computed(() => props.type)

  // 是否显示权限选择
  const shouldShowPermissions = computed(() => {
    // 超级管理员不需要选择权限（拥有所有权限）
    return formData.role !== 'SUPER_ADMIN'
  })

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    username: '',
    email: '',
    phone: '',
    role: 'MEMBER',
    password: '',
    managedById: null as number | null,
    permissionIds: [] as number[],
    responsibleMemberIds: [] as number[],
    // 会员Profile数据
    memberProfile: {
      english_name: '',
      description: '',
      finance_auth_token: '',
      sku_prefix: '',
      payment_channel: '',
      parent_account: '',
      ems_vip_number: '',
      ems_customer_code: '',
      ems_version_info: '',
      international_eub_us: '',
      ems_auth_token: '',
      amazon_username: '',
      amazon_seller_id: '',
      amazon_marketplace_id: '',
      aws_access_key_id: '',
      aws_secret_key: '',
      merchant_token: '',
      developer_aws_access: '',
      developer_secret_key: '',
      mws_auth_token: '',
      warehouse_location: '',
      status: 'active'
    },
    // 助理Profile数据
    assistantProfile: {
      description: '',
      related_finance_auth_token: '',
      internal_member_finance_auth_token: ''
    }
  })

  // 助理列表
  const assistantList = ref<Api.Common.AssistantInfo[]>([])

  // 会员列表
  const memberList = ref<Api.Common.MemberInfo[]>([])

  // 权限列表
  const permissionList = ref<Api.Common.PermissionInfo[]>([])
  const permissionCategories = ref<Record<string, Api.Common.PermissionInfo[]>>({})

  // 辅助函数：判断是否是助理角色
  const isAssistantRole = (role: string) => {
    return ['MEMBER_ASSISTANT', 'TRANSLATOR_ASSISTANT', 'DOCUMENT_ASSISTANT'].includes(role)
  }

  // 获取助理列表
  const getAssistantList = async () => {
    try {
      const result = await UserService.getAssistantList()
      assistantList.value = result
    } catch (error) {
      console.error('获取助理列表失败:', error)
    }
  }

  // 获取会员列表
  const getMemberList = async () => {
    try {
      const result = await UserService.getMemberList()
      memberList.value = result
    } catch (error) {
      console.error('获取会员列表失败:', error)
    }
  }

  // 获取权限列表
  const getPermissionList = async () => {
    try {
      const result = await UserService.getPermissionList()
      permissionList.value = result.permissions
      permissionCategories.value = result.categories
    } catch (error) {
      console.error('获取权限列表失败:', error)
    }
  }

  // 角色变化处理
  const handleRoleChange = (role: string) => {
    // 清空相关字段
    formData.managedById = null
    formData.responsibleMemberIds = []
    formData.permissionIds = []

    if (role === 'MEMBER') {
      // 当选择会员角色时，获取助理列表
      getAssistantList()
    } else if (role === 'MEMBER_ASSISTANT') {
      // 当选择助理角色时，获取会员列表
      getMemberList()
    }

    // 获取权限列表（除了超级管理员）
    if (role !== 'SUPER_ADMIN') {
      getPermissionList()
    }
  }

  // 表单验证规则
  const rules: FormRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    phone: [
      { required: false, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
    ],
    role: [{ required: true, message: '请选择角色', trigger: 'blur' }],

    // 会员Profile验证规则
    'memberProfile.english_name': [
      { max: 100, message: '英文名长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.sku_prefix': [
      { max: 50, message: 'SKU前缀长度不能超过50个字符', trigger: 'blur' }
    ],
    'memberProfile.payment_channel': [
      { max: 100, message: '支付渠道长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.parent_account': [
      { max: 100, message: '父账号长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.ems_vip_number': [
      { max: 100, message: 'EMS大客户号长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.ems_customer_code': [
      { max: 100, message: 'EMS客户代码长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.amazon_username': [
      { max: 100, message: '亚马逊用户名长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.amazon_seller_id': [
      { max: 100, message: '亚马逊Seller ID长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.amazon_marketplace_id': [
      { max: 100, message: '亚马逊Marketplace ID长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.warehouse_location': [
      { max: 100, message: '产品所在仓库长度不能超过100个字符', trigger: 'blur' }
    ],
    'memberProfile.status': [
      { required: true, message: '请选择状态', trigger: 'change' }
    ],

    // 助理Profile验证规则
    'assistantProfile.description': [
      { max: 500, message: '说明长度不能超过500个字符', trigger: 'blur' }
    ]
  }

  // 初始化表单数据
  const initFormData = async () => {
    const isEdit = props.type === 'edit' && props.userData
    const row = props.userData

    Object.assign(formData, {
      username: isEdit ? row.userName || '' : '',
      email: isEdit ? row.userEmail || '' : '',
      phone: isEdit ? row.userPhone || '' : '',
      role: isEdit ? row.role || 'MEMBER' : 'MEMBER',
      password: '',
      managedById: null,
      permissionIds: [],
      responsibleMemberIds: []
    })

    // 编辑模式下加载用户详情
    if (isEdit && row.id) {
      try {
        const userDetail = await UserService.getUserDetail(row.id)
        const userData = userDetail.data

        // 更新表单数据
        Object.assign(formData, {
          username: userData.username,
          email: userData.email,
          phone: userData.phone,
          role: userData.role,
          managedById: userData.managedBy?.id || null,
          permissionIds: userData.permissions.map(p => p.id),
          responsibleMemberIds: userData.responsibleMembers.map(m => m.id)
        })
      } catch (error) {
        console.error('加载用户详情失败:', error)
        ElMessage.error('加载用户详情失败')
      }
    }

    // 根据角色初始化相关数据
    if (formData.role === 'MEMBER') {
      getAssistantList()
    } else if (formData.role === 'MEMBER_ASSISTANT') {
      getMemberList()
    }

    // 获取权限列表（除了超级管理员）
    if (formData.role !== 'SUPER_ADMIN') {
      getPermissionList()
    }
  }

  // 统一监听对话框状态变化
  watch(
    () => [props.visible, props.type, props.userData],
    ([visible]) => {
      if (visible) {
        initFormData()
        nextTick(() => {
          formRef.value?.clearValidate()
        })
      }
    },
    { immediate: true }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      if (dialogType.value === 'add') {
        // 新增用户
        const params: any = {
          username: formData.username,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          password: formData.password || undefined,
          managed_by_id: formData.role === 'MEMBER' ? (formData.managedById || undefined) : undefined,
          permission_ids: formData.role !== 'SUPER_ADMIN' ? formData.permissionIds : undefined,
          responsible_member_ids: isAssistantRole(formData.role) ? formData.responsibleMemberIds : undefined
        }

        // 添加Profile数据
        if (formData.role === 'MEMBER') {
          params.member_profile = formData.memberProfile
        } else if (isAssistantRole(formData.role)) {
          params.assistant_profile = formData.assistantProfile
        }

        const result = await UserService.createUser(params)
        ElMessage.success(`用户创建成功！默认密码：${result.defaultPassword}`)
      } else {
        // 更新用户
        const params: any = {
          username: formData.username,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          permission_ids: formData.role !== 'SUPER_ADMIN' ? formData.permissionIds : undefined,
          responsible_member_ids: isAssistantRole(formData.role) ? formData.responsibleMemberIds : undefined
        }

        // 添加Profile数据
        if (formData.role === 'MEMBER') {
          params.member_profile = formData.memberProfile
        } else if (isAssistantRole(formData.role)) {
          params.assistant_profile = formData.assistantProfile
        }

        await UserService.updateUser(props.userData.id, params)
        ElMessage.success('用户更新成功')
      }

      dialogVisible.value = false
      emit('submit')
    } catch (error: any) {
      console.error('提交失败:', error)
      ElMessage.error(error.message || '操作失败，请重试')
    }
  }
</script>
