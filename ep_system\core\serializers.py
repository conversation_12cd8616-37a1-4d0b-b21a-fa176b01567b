from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import CustomUser, MemberProfile, AssistantProfile
from .middleware import get_current_region

class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器 - 支持区域感知注册"""
    
    password = serializers.CharField(
        write_only=True, 
        min_length=8, 
        style={'input_type': 'password'}
    )
    password2 = serializers.CharField(
        write_only=True, 
        style={'input_type': 'password'}
    )
    
    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'password', 'password2', 'phone')
        extra_kwargs = {
            'email': {'required': True},
            'phone': {'required': False}
        }

    def validate(self, attrs):
        """验证密码一致性和区域要求"""
        
        # 1. 验证两次密码是否一致
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({
                'password2': '两次输入的密码不一致'
            })
        
        # 2. 验证密码强度
        try:
            validate_password(attrs['password'])
        except ValidationError as e:
            raise serializers.ValidationError({
                'password': list(e.messages)
            })
        
        # 3. 获取当前区域，确保区域感知
        region = get_current_region()
        if not region:
            raise serializers.ValidationError({
                'region': '注册请求必须包含有效的区域代码(X-Region-Code请求头)'
            })
        
        # 将区域信息保存到验证后的数据中，供create方法使用
        attrs['region'] = region
        
        return attrs

    def validate_username(self, value):
        """验证用户名在当前区域内是否唯一"""
        region = get_current_region()
        if region:
            # 使用区域感知管理器检查用户名是否已存在
            if CustomUser.objects.filter(username=value).exists():
                raise serializers.ValidationError(
                    f'用户名 "{value}" 在当前区域已存在'
                )
        return value

    def validate_email(self, value):
        """验证邮箱在当前区域内是否唯一"""
        region = get_current_region()
        if region:
            # 使用区域感知管理器检查邮箱是否已存在
            if CustomUser.objects.filter(email=value).exists():
                raise serializers.ValidationError(
                    f'邮箱 "{value}" 在当前区域已存在'
                )
        return value

    def create(self, validated_data):
        """创建新用户，自动关联区域和默认角色"""
        
        # 移除确认密码字段
        validated_data.pop('password2', None)
        
        # 获取区域信息
        region = validated_data.pop('region')
        
        # 提取密码
        password = validated_data.pop('password')
        
        # 创建用户实例
        user = CustomUser(
            **validated_data,
            region=region,
            role=CustomUser.Role.MEMBER  # 默认为普通会员
        )
        
        # 设置加密密码
        user.set_password(password)
        user.save()
        
        return user

    def to_representation(self, instance):
        """自定义返回数据，不包含敏感信息"""
        data = super().to_representation(instance)
        # 移除密码字段
        data.pop('password', None)
        data.pop('password2', None)
        
        # 添加一些有用的信息
        data.update({
            'id': instance.id,
            'region': instance.region.name if instance.region else None,
            'region_code': instance.region.code if instance.region else None,
            'role': instance.get_role_display(),
            'date_joined': instance.date_joined.isoformat() if instance.date_joined else None
        })
        
        return data


class MemberProfileSerializer(serializers.ModelSerializer):
    """会员资料序列化器"""

    class Meta:
        model = MemberProfile
        fields = [
            'english_name', 'description', 'finance_auth_token', 'sku_prefix',
            'payment_channel', 'parent_account', 'ems_vip_number', 'ems_customer_code',
            'ems_version_info', 'international_eub_us', 'ems_auth_token',
            'amazon_username', 'amazon_seller_id', 'amazon_marketplace_id',
            'aws_access_key_id', 'aws_secret_key', 'merchant_token',
            'developer_aws_access', 'developer_secret_key', 'mws_auth_token',
            'warehouse_location', 'status'
        ]
        extra_kwargs = {
            # 敏感字段设置为write_only
            'finance_auth_token': {'write_only': True},
            'ems_auth_token': {'write_only': True},
            'aws_access_key_id': {'write_only': True},
            'aws_secret_key': {'write_only': True},
            'merchant_token': {'write_only': True},
            'developer_aws_access': {'write_only': True},
            'developer_secret_key': {'write_only': True},
            'mws_auth_token': {'write_only': True},
        }


class AssistantProfileSerializer(serializers.ModelSerializer):
    """助理资料序列化器"""

    class Meta:
        model = AssistantProfile
        fields = [
            'description', 'related_finance_auth_token', 'internal_member_finance_auth_token'
        ]
        extra_kwargs = {
            # 敏感字段设置为write_only
            'related_finance_auth_token': {'write_only': True},
            'internal_member_finance_auth_token': {'write_only': True},
        }


class UserDetailSerializer(serializers.ModelSerializer):
    """用户详情序列化器 - 支持Profile数据"""

    member_profile = MemberProfileSerializer(read_only=True)
    assistant_profile = AssistantProfileSerializer(read_only=True)

    class Meta:
        model = CustomUser
        fields = [
            'id', 'username', 'email', 'phone', 'role', 'region',
            'date_joined', 'last_login', 'is_active',
            'member_profile', 'assistant_profile'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def to_representation(self, instance):
        """根据角色动态返回Profile数据"""
        data = super().to_representation(instance)

        # 添加角色显示名称
        data['role_display'] = instance.get_role_display()
        data['region_name'] = instance.region.name if instance.region else None
        data['region_code'] = instance.region.code if instance.region else None

        # 根据角色决定是否包含Profile数据
        if instance.role == CustomUser.Role.MEMBER:
            # 会员角色：包含member_profile，移除assistant_profile
            data.pop('assistant_profile', None)
        elif instance.role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ]:
            # 助理角色：包含assistant_profile，移除member_profile
            data.pop('member_profile', None)
        else:
            # 其他角色：移除所有Profile数据
            data.pop('member_profile', None)
            data.pop('assistant_profile', None)

        return data


class UserCreateUpdateSerializer(serializers.ModelSerializer):
    """用户创建/更新序列化器 - 支持Profile数据"""

    member_profile = MemberProfileSerializer(required=False)
    assistant_profile = AssistantProfileSerializer(required=False)
    password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = CustomUser
        fields = [
            'username', 'email', 'phone', 'role', 'password',
            'member_profile', 'assistant_profile'
        ]

    def validate(self, attrs):
        """验证数据"""
        role = attrs.get('role')
        member_profile = attrs.get('member_profile')
        assistant_profile = attrs.get('assistant_profile')

        # 根据角色验证Profile数据
        if role == CustomUser.Role.MEMBER and not member_profile:
            # 会员角色可以没有Profile数据（可选）
            pass
        elif role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ] and not assistant_profile:
            # 助理角色可以没有Profile数据（可选）
            pass

        return attrs

    def create(self, validated_data):
        """创建用户和Profile"""
        member_profile_data = validated_data.pop('member_profile', None)
        assistant_profile_data = validated_data.pop('assistant_profile', None)
        password = validated_data.pop('password', None)

        # 获取当前区域
        region = get_current_region()
        if region:
            validated_data['region'] = region

        # 创建用户
        user = CustomUser.objects.create(**validated_data)
        if password:
            user.set_password(password)
            user.save()

        # 根据角色创建对应的Profile
        if user.role == CustomUser.Role.MEMBER and member_profile_data:
            MemberProfile.objects.create(user=user, **member_profile_data)
        elif user.role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ] and assistant_profile_data:
            AssistantProfile.objects.create(user=user, **assistant_profile_data)

        return user

    def update(self, instance, validated_data):
        """更新用户和Profile"""
        member_profile_data = validated_data.pop('member_profile', None)
        assistant_profile_data = validated_data.pop('assistant_profile', None)
        password = validated_data.pop('password', None)

        # 更新用户基础信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()

        # 更新Profile数据
        if instance.role == CustomUser.Role.MEMBER and member_profile_data:
            profile, created = MemberProfile.objects.get_or_create(user=instance)
            for attr, value in member_profile_data.items():
                setattr(profile, attr, value)
            profile.save()
        elif instance.role in [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ] and assistant_profile_data:
            profile, created = AssistantProfile.objects.get_or_create(user=instance)
            for attr, value in assistant_profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance