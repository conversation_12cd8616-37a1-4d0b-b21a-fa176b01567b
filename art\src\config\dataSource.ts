/**
 * 数据源配置
 * 控制前端使用Mock数据还是真实API数据
 */

export interface DataSourceConfig {
  // 用户管理模块
  userManagement: {
    useRealAPI: boolean
    mockFallback: boolean  // 当真实API失败时是否回退到Mock数据
  }
  
  // 菜单系统
  menuSystem: {
    useRealAPI: boolean
    mockFallback: boolean
  }
  
  // 业务模块
  businessModule: {
    useRealAPI: boolean
    mockFallback: boolean
  }
}

// 数据源配置
export const dataSourceConfig: DataSourceConfig = {
  // 用户管理 - 已对接真实API
  userManagement: {
    useRealAPI: true,
    mockFallback: true  // 保留Mock作为备选
  },
  
  // 菜单系统 - 支持动态菜单（可选）
  menuSystem: {
    useRealAPI: false,  // 设为true启用动态菜单
    mockFallback: true
  },

  // 业务模块 - 已对接真实API
  businessModule: {
    useRealAPI: true,
    mockFallback: true
  }
}

// 获取数据源配置的工具函数
export const getDataSourceConfig = () => dataSourceConfig

// 检查是否使用真实API
export const useRealAPI = (module: keyof DataSourceConfig): boolean => {
  return dataSourceConfig[module].useRealAPI
}

// 检查是否启用Mock回退
export const useMockFallback = (module: keyof DataSourceConfig): boolean => {
  return dataSourceConfig[module].mockFallback
}
