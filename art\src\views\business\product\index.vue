<!-- 产品管理页面 -->
<template>
  <div class="product-page art-full-height">
    <!-- 搜索栏 -->
    <ElCard shadow="never" style="margin-bottom: 16px">
      <ElForm :model="searchForm" inline>
        <ElFormItem label="产品名称">
          <ElInput v-model="searchForm.name" placeholder="请输入产品名称" clearable />
        </ElFormItem>
        <ElFormItem label="产品类别">
          <ElInput v-model="searchForm.category" placeholder="请输入产品类别" clearable />
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </ElCard>

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <div style="margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center">
        <div>
          <ElButton type="primary" @click="handleAdd">新增产品</ElButton>
          <ElButton @click="handleCreateTestData">创建测试数据</ElButton>
        </div>
        <div>
          <ElButton @click="handleRefresh">刷新</ElButton>
        </div>
      </div>

      <!-- 表格 -->
      <ElTable :data="tableData" :loading="loading" border>
        <ElTableColumn type="index" label="序号" width="60" />
        <ElTableColumn prop="product_code" label="产品编码" width="120" />
        <ElTableColumn prop="name" label="产品名称" min-width="150" />
        <ElTableColumn prop="category" label="产品类别" width="120" />
        <ElTableColumn prop="unit" label="单位" width="80" />
        <ElTableColumn prop="price" label="单价" width="100" align="right">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="cost" label="成本" width="100" align="right">
          <template #default="{ row }">
            ¥{{ row.cost }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <ElTableColumn prop="created_at" label="创建时间" width="160" />
        <ElTableColumn label="状态" width="80">
          <template #default="{ row }">
            <ElTag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '正常' : '禁用' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <ElButton type="primary" size="small" @click="handleEdit(row)">编辑</ElButton>
            <ElButton type="danger" size="small" @click="handleDelete(row)">删除</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div style="margin-top: 16px; display: flex; justify-content: center">
        <ElPagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ProductService, TestDataService } from '@/api/businessApi'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({ name: 'BusinessProduct' })

// 响应式数据
const loading = ref(false)
const tableData = ref<Api.Business.ProductListItem[]>([])

// 搜索表单
const searchForm = reactive({
  name: '',
  category: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 获取产品列表
const getProductList = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      name: searchForm.name,
      category: searchForm.category
    }
    
    const response = await ProductService.getProductList(params)
    tableData.value = response.records
    pagination.total = response.total
  } catch (error) {
    ElMessage.error('获取产品列表失败')
    console.error('获取产品列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getProductList()
}

// 重置
const handleReset = () => {
  searchForm.name = ''
  searchForm.category = ''
  pagination.current = 1
  getProductList()
}

// 刷新
const handleRefresh = () => {
  getProductList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.current = 1
  getProductList()
}

// 当前页改变
const handleCurrentChange = (current: number) => {
  pagination.current = current
  getProductList()
}

// 新增产品
const handleAdd = () => {
  ElMessage.info('新增产品功能开发中...')
}

// 编辑产品
const handleEdit = (row: Api.Business.ProductListItem) => {
  ElMessage.info(`编辑产品: ${row.name}`)
}

// 删除产品
const handleDelete = (row: Api.Business.ProductListItem) => {
  ElMessageBox.confirm(`确定要删除产品 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.info('删除功能开发中...')
  })
}

// 创建测试数据
const handleCreateTestData = async () => {
  try {
    loading.value = true
    await TestDataService.createTestData()
    ElMessage.success('测试数据创建成功')
    getProductList()
  } catch (error) {
    ElMessage.error('创建测试数据失败')
    console.error('创建测试数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  getProductList()
})
</script>

<style lang="scss" scoped>
.product-page {
  padding: 16px;
}
</style>
