# business/views.py
"""
业务数据API视图
演示分库架构的功能
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db import connections
from django.conf import settings
from core.middleware import get_current_region
from core.database_router import DatabaseManager
from .models import Customer, Product, Order, Inventory
import random
import string


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def database_status(request):
    """
    获取数据库状态信息
    """
    try:
        current_region = get_current_region()
        current_db = DatabaseManager.get_current_db()
        
        # 获取所有数据库连接状态
        db_status = {}
        for db_alias in settings.DATABASES.keys():
            try:
                connection = connections[db_alias]
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    db_status[db_alias] = {
                        'status': 'connected',
                        'database_name': settings.DATABASES[db_alias]['NAME']
                    }
            except Exception as e:
                db_status[db_alias] = {
                    'status': 'error',
                    'error': str(e),
                    'database_name': settings.DATABASES[db_alias]['NAME']
                }
        
        return Response({
            'current_region': {
                'code': current_region.code if current_region else None,
                'name': current_region.name if current_region else None,
            },
            'current_database': current_db,
            'databases': db_status,
            'total_databases': len(settings.DATABASES)
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_test_data(request):
    """
    在当前区域数据库中创建测试数据
    """
    try:
        current_region = get_current_region()
        if not current_region:
            return Response({
                'error': '无法确定当前区域'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        current_db = DatabaseManager.get_current_db()
        
        # 生成随机数据
        def random_string(length=8):
            return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
        
        # 创建客户
        customer = Customer.objects.create(
            customer_code=f"CUST_{current_region.code}_{random_string(6)}",
            name=f"{current_region.name}测试客户_{random_string(4)}",
            contact_person=f"联系人_{random_string(3)}",
            phone=f"138{random.randint(10000000, 99999999)}",
            email=f"test_{random_string(4).lower()}@example.com",
            address=f"{current_region.name}市测试地址{random.randint(1, 999)}号",
            created_by_id=request.user.id
        )
        
        # 创建产品
        product = Product.objects.create(
            product_code=f"PROD_{current_region.code}_{random_string(6)}",
            name=f"{current_region.name}测试产品_{random_string(4)}",
            description=f"这是{current_region.name}区域的测试产品",
            category="测试类别",
            unit="个",
            price=random.randint(100, 10000) / 100,
            cost=random.randint(50, 5000) / 100,
            created_by_id=request.user.id
        )
        
        # 创建订单
        order = Order.objects.create(
            order_number=f"ORD_{current_region.code}_{random_string(8)}",
            customer=customer,
            status=Order.OrderStatus.PENDING,
            total_amount=product.price * 2,
            notes=f"来自{current_region.name}区域的测试订单",
            created_by_id=request.user.id
        )
        
        # 创建库存
        inventory = Inventory.objects.create(
            product=product,
            warehouse_location=f"{current_region.name}仓库A区",
            quantity_on_hand=random.randint(100, 1000),
            quantity_reserved=random.randint(0, 50),
            reorder_point=random.randint(10, 100),
            created_by_id=request.user.id
        )
        
        return Response({
            'message': f'测试数据创建成功！',
            'region': current_region.name,
            'database': current_db,
            'created_data': {
                'customer': {
                    'id': customer.id,
                    'code': customer.customer_code,
                    'name': customer.name
                },
                'product': {
                    'id': product.id,
                    'code': product.product_code,
                    'name': product.name
                },
                'order': {
                    'id': order.id,
                    'number': order.order_number,
                    'total': str(order.total_amount)
                },
                'inventory': {
                    'id': inventory.id,
                    'location': inventory.warehouse_location,
                    'quantity': str(inventory.quantity_on_hand)
                }
            }
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_region_data(request):
    """
    列出当前区域的数据
    """
    try:
        current_region = get_current_region()
        current_db = DatabaseManager.get_current_db()

        # 获取各种数据的数量
        customers_count = Customer.objects.count()
        products_count = Product.objects.count()
        orders_count = Order.objects.count()
        inventory_count = Inventory.objects.count()

        # 获取最近的数据
        recent_customers = Customer.objects.order_by('-created_at')[:5]
        recent_products = Product.objects.order_by('-created_at')[:5]
        recent_orders = Order.objects.order_by('-created_at')[:5]
        
        return Response({
            'region': {
                'code': current_region.code if current_region else None,
                'name': current_region.name if current_region else None,
            },
            'database': current_db,
            'statistics': {
                'customers': customers_count,
                'products': products_count,
                'orders': orders_count,
                'inventory': inventory_count
            },
            'recent_data': {
                'customers': [
                    {
                        'id': c.id,
                        'code': c.customer_code,
                        'name': c.name,
                        'created_at': c.created_at
                    } for c in recent_customers
                ],
                'products': [
                    {
                        'id': p.id,
                        'code': p.product_code,
                        'name': p.name,
                        'price': str(p.price),
                        'created_at': p.created_at
                    } for p in recent_products
                ],
                'orders': [
                    {
                        'id': o.id,
                        'number': o.order_number,
                        'customer': o.customer.name,
                        'total': str(o.total_amount),
                        'status': o.status,
                        'created_at': o.created_at
                    } for o in recent_orders
                ]
            }
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def customer_list(request):
    """
    客户列表API
    GET: 获取客户列表（支持分页和搜索）
    POST: 创建新客户
    """
    try:
        # 确保区域上下文正确设置
        current_region = get_current_region()
        current_db = DatabaseManager.get_current_db()

        if not current_region:
            return Response({
                'code': 400,
                'msg': '无法确定当前区域，请重新登录',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        if request.method == 'GET':
            # 获取查询参数
            current = int(request.GET.get('current', 1))
            size = int(request.GET.get('size', 10))
            name = request.GET.get('name', '')

            # 使用指定数据库连接构建查询
            queryset = Customer.objects.using(current_db).all()
            if name:
                queryset = queryset.filter(name__icontains=name)

            # 分页
            start = (current - 1) * size
            end = start + size
            customers = queryset[start:end]
            total = queryset.count()

            # 序列化数据
            customer_list = []
            for customer in customers:
                customer_list.append({
                    'id': customer.id,
                    'customer_code': customer.customer_code,
                    'name': customer.name,
                    'contact_person': customer.contact_person,
                    'phone': customer.phone,
                    'email': customer.email,
                    'address': customer.address,
                    'created_at': customer.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'is_active': customer.is_active,
                })

            return Response({
                'code': 200,
                'msg': '获取客户列表成功',
                'data': {
                    'records': customer_list,
                    'current': current,
                    'size': size,
                    'total': total
                }
            })

        elif request.method == 'POST':
            # 创建新客户
            customer = Customer.objects.using(current_db).create(
                customer_code=request.data.get('customer_code'),
                name=request.data.get('name'),
                contact_person=request.data.get('contact_person', ''),
                phone=request.data.get('phone', ''),
                email=request.data.get('email', ''),
                address=request.data.get('address', ''),
            )

            return Response({
                'code': 200,
                'msg': '客户创建成功',
                'data': {
                    'id': customer.id,
                    'customer_code': customer.customer_code,
                    'name': customer.name,
                }
            })

    except Exception as e:
        return Response({
            'code': 500,
            'msg': f'操作失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def product_list(request):
    """
    产品列表API
    GET: 获取产品列表（支持分页和搜索）
    POST: 创建新产品
    """
    try:
        # 确保区域上下文正确设置
        current_region = get_current_region()
        current_db = DatabaseManager.get_current_db()

        if not current_region:
            return Response({
                'code': 400,
                'msg': '无法确定当前区域，请重新登录',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        if request.method == 'GET':
            # 获取查询参数
            current = int(request.GET.get('current', 1))
            size = int(request.GET.get('size', 10))
            name = request.GET.get('name', '')
            category = request.GET.get('category', '')

            # 使用指定数据库连接构建查询
            queryset = Product.objects.using(current_db).all()
            if name:
                queryset = queryset.filter(name__icontains=name)
            if category:
                queryset = queryset.filter(category__icontains=category)

            # 分页
            start = (current - 1) * size
            end = start + size
            products = queryset[start:end]
            total = queryset.count()

            # 序列化数据
            product_list = []
            for product in products:
                product_list.append({
                    'id': product.id,
                    'product_code': product.product_code,
                    'name': product.name,
                    'description': product.description,
                    'category': product.category,
                    'unit': product.unit,
                    'price': str(product.price),
                    'cost': str(product.cost),
                    'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'is_active': product.is_active,
                })

            return Response({
                'code': 200,
                'msg': '获取产品列表成功',
                'data': {
                    'records': product_list,
                    'current': current,
                    'size': size,
                    'total': total
                }
            })

        elif request.method == 'POST':
            # 创建新产品
            product = Product.objects.using(current_db).create(
                product_code=request.data.get('product_code'),
                name=request.data.get('name'),
                description=request.data.get('description', ''),
                category=request.data.get('category', ''),
                unit=request.data.get('unit', '个'),
                price=request.data.get('price', 0),
                cost=request.data.get('cost', 0),
            )

            return Response({
                'code': 200,
                'msg': '产品创建成功',
                'data': {
                    'id': product.id,
                    'product_code': product.product_code,
                    'name': product.name,
                }
            })

    except Exception as e:
        return Response({
            'code': 500,
            'msg': f'操作失败: {str(e)}',
            'data': None
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
