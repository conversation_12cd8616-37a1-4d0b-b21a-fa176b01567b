from django.db import models

# Create your models here.
# core/models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from .managers import RegionAwareManager

class Region(models.Model):
    """区域模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name="区域名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="区域代码")
    def __str__(self):
        return self.name


class Permission(models.Model):
    """权限模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name="权限名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="权限代码")
    description = models.TextField(blank=True, verbose_name="权限描述")
    category = models.CharField(max_length=50, verbose_name="权限分类")

    class Meta:
        verbose_name = "权限"
        verbose_name_plural = "权限"

    def __str__(self):
        return f"{self.name} ({self.code})"


class UserPermission(models.Model):
    """用户权限关联模型"""
    user = models.ForeignKey('CustomUser', on_delete=models.CASCADE, related_name='permission_grants')
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name="授权时间")
    granted_by = models.ForeignKey('CustomUser', on_delete=models.SET_NULL, null=True, related_name='granted_permissions', verbose_name="授权人")

    class Meta:
        unique_together = ('user', 'permission')
        verbose_name = "用户权限"
        verbose_name_plural = "用户权限"

    def __str__(self):
        return f"{self.user.username} - {self.permission.name}"

class CustomUser(AbstractUser):
    """自定义用户模型"""
    
    class Role(models.TextChoices):
        SUPER_ADMIN = 'SUPER_ADMIN', '超级管理员'
        REGION_ADMIN = 'REGION_ADMIN', '区域管理员'
        MEMBER = 'MEMBER', '普通会员'
        MEMBER_ASSISTANT = 'MEMBER_ASSISTANT', '会员助理'
        WAREHOUSE_MANAGER = 'WAREHOUSE_MANAGER', '库管'
        TRANSLATOR_ASSISTANT = 'TRANSLATOR_ASSISTANT', '翻译助理'
        DOCUMENT_ASSISTANT = 'DOCUMENT_ASSISTANT', '文档助理'
        OPERATION_SPECIALIST = 'OPERATION_SPECIALIST', '精细化操作人员'
        FINANCE_STAFF = 'FINANCE_STAFF', '财务人员'

    # 每个用户都必须属于一个区域
    region = models.ForeignKey(Region, on_delete=models.PROTECT, null=True, blank=True, verbose_name="所属区域")
    # 可以添加手机号等其他字段
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="手机号")
    role = models.CharField(max_length=20, choices=Role.choices, default=Role.MEMBER, verbose_name="角色")
    email = models.EmailField(verbose_name="邮箱")

    # 会员助理关联：会员(MEMBER)通过此字段指向负责他们的助理(MEMBER_ASSISTANT)
    managed_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'role': Role.MEMBER_ASSISTANT},
        related_name='managed_members',
        verbose_name="负责助理",
        help_text="仅对会员(MEMBER)角色有效，指向负责该会员的助理"
    )


    # 指定默认的模型管理器
    objects = RegionAwareManager()  # 覆盖默认的
    all_objects = models.Manager()

    class Meta:
        # 添加复合唯一约束，确保 email 在每个 region 内是唯一的
        constraints = [
            models.UniqueConstraint(fields=['region', 'email'], name='unique_email_in_region')
        ]

    def __str__(self):
        return self.username

    def has_permission(self, permission_code):
        """检查用户是否有指定权限"""
        # 超级管理员拥有所有权限
        if self.role == self.Role.SUPER_ADMIN:
            return True

        # 检查用户是否被直接授予该权限
        return self.permission_grants.filter(permission__code=permission_code).exists()

    def get_permissions(self):
        """获取用户所有权限"""
        if self.role == self.Role.SUPER_ADMIN:
            # 超级管理员拥有所有权限
            return Permission.objects.all()

        # 返回用户被授予的权限
        return Permission.objects.filter(userpermission__user=self)

    def get_permission_codes(self):
        """获取用户权限代码列表"""
        if self.role == self.Role.SUPER_ADMIN:
            return list(Permission.objects.values_list('code', flat=True))

        return list(self.permission_grants.values_list('permission__code', flat=True))


class MemberProfile(models.Model):
    """会员专用资料模型"""
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='member_profile',
        limit_choices_to={'role': CustomUser.Role.MEMBER},
        verbose_name="关联用户"
    )

    # 基础信息
    english_name = models.CharField(max_length=100, blank=True, verbose_name="英文名(用于FBA发票)")
    description = models.TextField(blank=True, verbose_name="说明(可填写地区等其它信息)")

    # 财务相关
    finance_auth_token = models.CharField(max_length=255, blank=True, verbose_name="财务AuthToken")
    sku_prefix = models.CharField(max_length=50, blank=True, verbose_name="SKU前缀")
    payment_channel = models.CharField(max_length=100, blank=True, verbose_name="支付渠道")
    parent_account = models.CharField(max_length=100, blank=True, verbose_name="父账号")

    # EMS相关
    ems_vip_number = models.CharField(max_length=100, blank=True, verbose_name="EMS大客户号")
    ems_customer_code = models.CharField(max_length=100, blank=True, verbose_name="EMS客户代码")
    ems_version_info = models.CharField(max_length=100, blank=True, verbose_name="EMS版本信息")
    international_eub_us = models.CharField(max_length=100, blank=True, verbose_name="international_eub_us_1.1")
    ems_auth_token = models.CharField(max_length=255, blank=True, verbose_name="EMS authToken号")

    # 亚马逊相关
    amazon_username = models.CharField(max_length=100, blank=True, verbose_name="亚马逊用户名")
    amazon_seller_id = models.CharField(max_length=100, blank=True, verbose_name="亚马逊Seller ID")
    amazon_marketplace_id = models.CharField(max_length=100, blank=True, verbose_name="亚马逊Marketplace ID")

    # AWS相关
    aws_access_key_id = models.CharField(max_length=255, blank=True, verbose_name="AWS Access Key ID")
    aws_secret_key = models.CharField(max_length=255, blank=True, verbose_name="Secret Key")
    merchant_token = models.CharField(max_length=255, blank=True, verbose_name="Merchant Token")
    developer_aws_access = models.CharField(max_length=255, blank=True, verbose_name="开发者AWS Access")
    developer_secret_key = models.CharField(max_length=255, blank=True, verbose_name="开发者Secret Key")
    mws_auth_token = models.CharField(max_length=255, blank=True, verbose_name="MWS授权令牌")

    # 业务相关
    warehouse_location = models.CharField(max_length=100, blank=True, verbose_name="产品所在仓库")
    status = models.CharField(max_length=50, default='active', verbose_name="状态")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "会员资料"
        verbose_name_plural = "会员资料"

    def __str__(self):
        return f"{self.user.username} - 会员资料"


class AssistantProfile(models.Model):
    """助理专用资料模型"""
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='assistant_profile',
        limit_choices_to={'role__in': [
            CustomUser.Role.MEMBER_ASSISTANT,
            CustomUser.Role.TRANSLATOR_ASSISTANT,
            CustomUser.Role.DOCUMENT_ASSISTANT
        ]},
        verbose_name="关联用户"
    )

    # 助理专用字段
    description = models.TextField(blank=True, verbose_name="说明(可填写地区等其它信息)")
    related_finance_auth_token = models.CharField(max_length=255, blank=True, verbose_name="关联财务助理AuthToken")
    internal_member_finance_auth_token = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="内部会员财务AuthToken(内部会员的助理才有此项)"
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "助理资料"
        verbose_name_plural = "助理资料"

    def __str__(self):
        return f"{self.user.username} - 助理资料"


class Order(models.Model):
    """订单模型示例"""
    order_id = models.CharField(max_length=100, unique=True, verbose_name="订单号")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="金额")
    # 订单创建者
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name="关联用户")
    # 订单所属区域，通过 user 自动关联
    region = models.ForeignKey(Region, on_delete=models.PROTECT, verbose_name="所属区域")
    created_at = models.DateTimeField(auto_now_add=True)

    # 同样应用管理器
    objects = RegionAwareManager()
    all_objects = models.Manager()

    def save(self, *args, **kwargs):
        # 在保存订单时，自动将其区域设置为其关联用户的区域
        if not self.pk and self.user and self.user.region:
            self.region = self.user.region
        super().save(*args, **kwargs)

    def __str__(self):
        return self.order_id
