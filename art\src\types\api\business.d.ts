/**
 * 业务模块API类型定义
 */

declare namespace Api {
  namespace Business {
    // 客户相关类型
    interface CustomerListItem {
      id: number
      customer_code: string
      name: string
      contact_person: string
      phone: string
      email: string
      address: string
      created_at: string
      is_active: boolean
    }

    interface CustomerListData {
      records: CustomerListItem[]
      current: number
      size: number
      total: number
    }

    interface CreateCustomerParams {
      customer_code: string
      name: string
      contact_person?: string
      phone?: string
      email?: string
      address?: string
    }

    interface CreateCustomerResponse {
      id: number
      customer_code: string
      name: string
    }

    interface UpdateCustomerParams extends CreateCustomerParams {}
    interface UpdateCustomerResponse extends CreateCustomerResponse {}
    interface CustomerDetailResponse extends CustomerListItem {}
    interface DeleteCustomerResponse {
      message: string
    }

    // 产品相关类型
    interface ProductListItem {
      id: number
      product_code: string
      name: string
      description: string
      category: string
      unit: string
      price: string
      cost: string
      created_at: string
      is_active: boolean
    }

    interface ProductListData {
      records: ProductListItem[]
      current: number
      size: number
      total: number
    }

    interface CreateProductParams {
      product_code: string
      name: string
      description?: string
      category?: string
      unit?: string
      price?: number
      cost?: number
    }

    interface CreateProductResponse {
      id: number
      product_code: string
      name: string
    }

    interface UpdateProductParams extends CreateProductParams {}
    interface UpdateProductResponse extends CreateProductResponse {}
    interface ProductDetailResponse extends ProductListItem {}
    interface DeleteProductResponse {
      message: string
    }

    // 测试数据相关类型
    interface CreateTestDataResponse {
      message: string
      region: string
      database: string
      created_data: {
        customer: {
          id: number
          code: string
          name: string
        }
        product: {
          id: number
          code: string
          name: string
        }
        order: {
          id: number
          number: string
          total: string
        }
        inventory: {
          id: number
          location: string
          quantity: string
        }
      }
    }

    interface RegionDataOverviewResponse {
      region: string
      database: string
      data_counts: {
        customers: number
        products: number
        orders: number
        inventory: number
      }
      recent_data: {
        customers: Array<{
          id: number
          code: string
          name: string
          created_at: string
        }>
        products: Array<{
          id: number
          code: string
          name: string
          price: string
          created_at: string
        }>
        orders: Array<{
          id: number
          number: string
          customer: string
          total: string
          status: string
          created_at: string
        }>
      }
    }

    interface DatabaseStatusResponse {
      databases: Array<{
        name: string
        status: string
        tables: number
        region?: string
      }>
      current_region: string
      current_database: string
    }
  }
}
