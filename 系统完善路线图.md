## 🏗️ 系统整体架构

### 📁 项目结构

```
Dawn_ERP_V1.0/
├── art/                    # 前端项目 (Vue3 + TypeScript + Element Plus)
│   ├── src/
│   │   ├── views/system/user/    # 用户管理页面
│   │   ├── store/               # Pinia状态管理
│   │   ├── router/              # 路由配置
│   │   ├── api/                 # API接口
│   │   └── utils/               # 工具函数
│   └── vite.config.ts          # Vite配置
└── ep_system/              # 后端项目 (Django + DRF)
    ├── core/               # 核心模块 (用户认证、权限)
    ├── business/           # 业务模块 (客户、产品、订单)
    └── ep_system/          # Django配置
```

### 🎯 核心特性



#### 1. **多数据库架构** 🗄️

- **主数据库 (ep_system_master)**: 存储用户认证、区域配置等全局数据
- **5个区域数据库**: MPR、RL、EO、ZZ、WH，每个区域物理隔离
- **智能路由**: 根据用户区域自动选择对应数据库
- **数据隔离**: 每个区域用户只能访问自己区域的业务数据

#### 2. **用户认证与权限系统** 👤

- **角色体系**: 超级管理员、区域管理员、会员、会员助理、库管
- **区域绑定**: 用户与特定区域绑定，实现数据访问控制
- **JWT认证**: 使用Token进行身份验证
- **权限控制**: 基于角色的细粒度权限管理

#### 3. **业务模块** 📊

- **Customer**: 客户管理
- **Product**: 产品管理
- **Order**: 订单管理
- **OrderItem**: 订单明细
- **Inventory**: 库存管理

#### 4. **前端技术栈** 🎨

- **Vue 3 + TypeScript**: 现代化前端框架
- **Element Plus**: UI组件库
- **Pinia**: 状态管理
- **Vite**: 构建工具
- **Vue Router**: 路由管理

### 🔧 技术实现亮点

#### 数据库路由器

```python
class RegionDatabaseRouter:
    # 主库模型：用户认证、区域配置
    MASTER_DB_MODELS = {'core.CustomUser', 'core.Region', ...}
    # 区域数据库映射
    REGION_DB_MAPPING = {'MPR': 'mpr_db', 'RL': 'rl_db', ...}
```

#### 区域感知管理器

```python
class RegionAwareManager:
    def get_queryset(self):
        region = get_current_region()
        if region:
            return queryset.filter(region=region)
```

### 📋 当前系统状态

#### ✅ 已完成功能

1. **多数据库架构**: 完整实现并测试通过
2. **用户认证系统**: JWT登录、权限控制
3. **区域数据隔离**: 物理层面完全分离
4. **基础业务模型**: Customer、Product、Order等
5. **前端用户管理**: 完整的用户CRUD界面
6. **API接口**: RESTful API设计



#### 🔄 需要关注的点

1. **前后端对接**: 前端目前使用Mock数据，需要对接真实后端API
2. **环境配置**: 前端API地址指向Mock服务
3. **数据同步**: 前端用户管理与后端用户系统的完整对接
4. **权限前端**: 前端权限控制的完善

### 💡 系统优势

- **高性能**: 多数据库分离，支持百万级数据
- **高安全**: 区域数据物理隔离
- **高扩展**: 模块化设计，易于扩展
- **现代化**: 使用最新技术栈



## 📋 **系统完善路线图**

基于您的架构设计，我建议按以下顺序逐步完善：

### 🎯 **第一阶段：完善角色体系**

1. 扩展用户角色定义
   - 添加会员（MEMBER）
   - 添加会员助理（MEMBER_ASSISTANT）
   - 添加库管（WAREHOUSE_MANAGER）
   - 更新数据库模型和权限映射
2. 完善权限控制
   - 定义每个角色的具体权限
   - 更新前端路由权限配置
   - 实现角色级别的功能访问控制

### 🎯 **第二阶段：实现域名路由**

1. 多域名支持
   - 配置域名识别中间件
   - 实现根据域名自动识别区域
   - 更新前端配置支持多域名
2. 区域自动切换
   - 根据访问域名自动设置区域上下文
   - 确保数据查询自动过滤到对应区域

### 🎯 **第三阶段：精细化权限控制**

1. 用户级数据隔离
   - 实现用户只能访问自己数据的逻辑
   - 会员助理访问权限控制
   - 数据查询自动添加用户过滤条件

### 🎯 **第四阶段：业务模块开发**

1. 核心业务功能
   - 客户管理模块
   - 订单管理模块
   - 库存管理模块
   - 报表统计模块

仓库管理

SKU产品管理

FBA发货任务管理

FBA库存管理



精细化操作

车间管理

需要上传的产品

调货

助理SKU统计

通知管理

填写转单号及运费

采集亚马逊产品

我的产品

工作安排及工作日志

渠道及海关编码

虎门发货操作

郑州发货操作

运费对照

UPC码管理

批量上传



仓库管理

- 仓库产品
- 按货架查看
- 缺货产品
- 供货商管理
- 新建仓库产品
- 查看进货记录/入库
- 查看账单记录
- 库存日志
- 库位日志
- 抓取SKU添加产品
- 查找不发货的产品
- 库存差异

SKU产品管理

- 查看SKU产品
- FNSKU入夹
- 查看FNSKU
- 被跟卖SKU
- SKU发货汇总
- 添加SKU产品
- 抓取SKU
- 优化SKU
- 查看提醒-可选
- 添加提醒-可选
- 查找不发货的SKU
- SKU关键字-可选
- SKU修复-可选
- 关闭广告的SKU
- 提交的跟卖-可选

FBA发货任务管理

- 查看任务
- 添加任务
- 任务草稿
- 任务排序
- FBA运费
- 运输状态
- 合并发货
- 移除的产品
- 任务发货汇总
- 任务发货记录
- 导入发货任务
- 超级删除
- 海运数据
- FBA运单号上传

FBA库存管理

- [查看FBA库存](http://mpr.hkzhenshang.com/FBA/fba_sku_list.php)
- [自定义销量查看](http://mpr.hkzhenshang.com/FBA/fba_sku_customize.php)
- [自定义状态](http://mpr.hkzhenshang.com/FBA/ad_sku_delimit.php)
- [更新发货SKU](http://mpr.hkzhenshang.com/FBA/fba_sku_show_up.php)
- [需要发货的SKU](http://mpr.hkzhenshang.com/FBA/fba_sku_delivery.php)
- [不再提示的SKU](http://mpr.hkzhenshang.com/FBA/fba_sku_delivery.php?status=5)
- [更新库存数量](http://mpr.hkzhenshang.com/FBA/Update_stock.php)
- [获取库存销售状况](http://mpr.hkzhenshang.com/FBA/select_user_get_fba.php)
- [创建库存销售状况请求](http://mpr.hkzhenshang.com/FBA/select_user_set_fba.php)
- [获取亚马逊货件](http://mpr.hkzhenshang.com/FBA/InboundShipments.php)
- [获取库存销售(替代方案)](http://mpr.hkzhenshang.com/Sales_FBA/Sales_sku_sales.php)

精细化操作

- [SKU情况报告](http://mpr.hkzhenshang.com/sku/sku_profit.php?condition=1)
- [SKU重测](http://mpr.hkzhenshang.com/Retest/Retest_list.php)
- [添加不可售SKU](http://mpr.hkzhenshang.com/p/non_salable/add.php)
- [查看不售SKU](http://mpr.hkzhenshang.com/p/non_salable/sku_non_salable.php)
- [SKU冗余](http://mpr.hkzhenshang.com/p/non_salable/redundancy_sku.php)

车间管理

- [添加车间任务](http://mpr.hkzhenshang.com/workshop/add.php)
- [车间任务](http://mpr.hkzhenshang.com/workshop/list.php)
- [任务草稿](http://mpr.hkzhenshang.com/workshop/list.php?draft=1)

需要上传的产品

- [查看所有产品](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?Archive=0)
- [产品草稿](http://mpr.hkzhenshang.com/add_pro/pro_draft.php)
- [待审核](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=1&Archive=0)
- [添加产品资料](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=10&Archive=0)
- [资料审核](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=11&Archive=0)
- [修改产品资料](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=7&Archive=0)
- [产品资料再审](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=9&Archive=0)
- [已过审核](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=2&Archive=0)
- [长尾词已添加](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=12&Archive=0)
- [已翻译](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=4&Archive=0)
- [制作文档完成](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=8&Archive=0)
- [已上传](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=6&Archive=0)
- [已完成](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=5&Archive=0)
- [未通过审核](http://mpr.hkzhenshang.com/add_pro/pro_manage.php?strstatus=3&Archive=0)
- [模板](http://mpr.hkzhenshang.com/add_pro/template.php)
- [模板分类](http://mpr.hkzhenshang.com/add_pro/template_class.php)
- [是否需要添加长尾词](http://mpr.hkzhenshang.com/add_pro/Long_tail_switch.php)
- [美工/翻译统计](http://mpr.hkzhenshang.com/add_pro/Art_Statistics.php)

调货

- [发起调货](http://mpr.hkzhenshang.com/Transfer/initiate.php)
- [我发起的调货](http://mpr.hkzhenshang.com/Transfer/my_initiate.php)
- [需要我审核的调货](http://mpr.hkzhenshang.com/Transfer/my_review.php)
- [关于我的调货](http://mpr.hkzhenshang.com/Transfer/my_Transfer.php)
- [所有调货](http://mpr.hkzhenshang.com/Transfer/Transfer_all.php)

助理SKU统计

- [新SKU统计](http://mpr.hkzhenshang.com/count/new_sku_count.php)
- [查看SKU销量](http://mpr.hkzhenshang.com/count/sku_sales_list.php)
- [生成SKU销量](http://mpr.hkzhenshang.com/count/sku_sales.php)
- [生成SKU销量(新版)](http://mpr.hkzhenshang.com/count/sku_sales_ifam.php)
- [SKU销量统计（测试）](http://mpr.hkzhenshang.com/count/New_SKU_sales.php)

通知管理

- [查看通知](http://mpr.hkzhenshang.com/Notice/Notice.php)
- [发送通知](http://mpr.hkzhenshang.com/Notice/send.php)
- [我通知](http://mpr.hkzhenshang.com/Notice/my_Notice.php)

填写转单号及运费

- [未转单](http://mpr.hkzhenshang.com/Freight_up/index.php?f=1)
- [已转单](http://mpr.hkzhenshang.com/Freight_up/index.php?f=2)

采集亚马逊产品

- [添加采集](http://mpr.hkzhenshang.com/collection/up_url.php)
- [采集箱](http://mpr.hkzhenshang.com/collection/collection_box.php)
- [可分配的产品](http://mpr.hkzhenshang.com/collection/collection_box.php?collect_status=4&if_allocation=0)

我的产品

- [添加产品](http://mpr.hkzhenshang.com/add_pro/add_pro.php)
- [所有产品](http://mpr.hkzhenshang.com/add_pro/user_pro_manage.php)
- [待审核](http://mpr.hkzhenshang.com/add_pro/user_pro_manage.php?status=1)
- [已过审核](http://mpr.hkzhenshang.com/add_pro/user_pro_manage.php?status=2)
- [已翻译](http://mpr.hkzhenshang.com/add_pro/user_pro_manage.php?status=4)
- [已完成](http://mpr.hkzhenshang.com/add_pro/user_pro_manage.php?status=5)
- [未通过审核](http://mpr.hkzhenshang.com/add_pro/user_pro_manage.php?status=3)

工作安排及工作日志

- [添加](http://mpr.hkzhenshang.com/msg/add_msg.php)
- [工作日志(备忘)](http://mpr.hkzhenshang.com/msg/Logbook.php)
- [我安排的工作](http://mpr.hkzhenshang.com/msg/work_arrangements.php)
- [我的工作](http://mpr.hkzhenshang.com/msg/My_job.php)

渠道及海关编码

- [FBA渠道管理](http://mpr.hkzhenshang.com/task/channel_manage.php)
- [渠道分类](http://mpr.hkzhenshang.com/task/channel_class.php)
- [原单号管理](http://mpr.hkzhenshang.com/task/original_number.php)
- [FBA仓库地址](http://mpr.hkzhenshang.com/task/FBA_address.php)
- [海关编码](http://mpr.hkzhenshang.com/p/hs_code/hs_code.php)
- [添加海关编码](http://mpr.hkzhenshang.com/p/hs_code/add_hs_code.php)
- [发货箱管理](http://mpr.hkzhenshang.com/task/Shipping_boxes.php)
- [快递始发件地址](http://mpr.hkzhenshang.com/task/DHL_Shipper_addres.php)

订单管理

- [抓取订单](http://mpr.hkzhenshang.com/zom/getorderlist.php)
- [查看所有订单](http://mpr.hkzhenshang.com/order_manage.php)
- [查看待处理订单](http://mpr.hkzhenshang.com/order_manage.php?strstate=1)
- [查看包货SKU](http://mpr.hkzhenshang.com/ship_sku.php)
- [未下载](http://mpr.hkzhenshang.com/order_manage.php?strstate=2)
- [已下载](http://mpr.hkzhenshang.com/order_manage.php?strstate=3)
- [发货完成](http://mpr.hkzhenshang.com/order_manage.php?strstate=4)
- [取消订单](http://mpr.hkzhenshang.com/order_manage.php?strstate=5)
- [保留订单](http://mpr.hkzhenshang.com/order_manage.php?Retention=1)
- [计划设置](http://mpr.hkzhenshang.com/zom/ManageReport.php)
- [查看运单号](http://mpr.hkzhenshang.com/show_awb.php)
- [数据比较](http://mpr.hkzhenshang.com/Data_comparison.php)
- [数据提交](http://mpr.hkzhenshang.com/zom_up_list.php)

虎门发货操作

- [查看虎门包货SKU](http://mpr.hkzhenshang.com/ship_sku.php?str_add_type=1)
- [虎门未下载](http://mpr.hkzhenshang.com/order_manage.php?strstate=2&str_add_type=1)
- [虎门已下载](http://mpr.hkzhenshang.com/order_manage.php?strstate=3&str_add_type=1)
- [虎门发货完成](http://mpr.hkzhenshang.com/order_manage.php?strstate=4&str_add_type=1)
- [未实际发货订单](http://mpr.hkzhenshang.com/order_manage.php?str_add_type=1&ActualShip=0)
- [打印未发货订单](http://mpr.hkzhenshang.com/order_manage.php?strstate=4&str_add_type=1&ActualShip=0)
- [扫描发货](http://mpr.hkzhenshang.com/Scan_ship.php)

郑州发货操作

- [查看郑州包货SKU](http://mpr.hkzhenshang.com/ship_sku.php?str_add_type=2)
- [郑州未下载](http://mpr.hkzhenshang.com/order_manage.php?strstate=2&str_add_type=2)
- [郑州已下载](http://mpr.hkzhenshang.com/order_manage.php?strstate=3&str_add_type=2)
- [郑州发货完成](http://mpr.hkzhenshang.com/order_manage.php?strstate=4&str_add_type=2)
- [未实际发货订单](http://mpr.hkzhenshang.com/order_manage.php?str_add_type=2&ActualShip=0)
- [打印未发货订单](http://mpr.hkzhenshang.com/order_manage.php?strstate=4&str_add_type=2&ActualShip=0)
- [扫描发货](http://mpr.hkzhenshang.com/Scan_ship.php)

运费对照

- [上传运费](http://mpr.hkzhenshang.com/fare/up.php)
- [上传预计费用](http://mpr.hkzhenshang.com/fare/Estimated_up.php)
- [查看运费](http://mpr.hkzhenshang.com/fare/list.php)
- [查看以前运费](http://mpr.hkzhenshang.com/fare/list_old.php)

UPC码管理

- [查看UPC码](http://mpr.hkzhenshang.com/upc/upc_manage.php)
- [添加UPC码](http://mpr.hkzhenshang.com/upc/upc_add.php)

批量上传

- [查看文件](http://mpr.hkzhenshang.com/ama_template/list.php)
- [添加上传](http://mpr.hkzhenshang.com/ama_template/add_step1.php)
- [查看模板](http://mpr.hkzhenshang.com/ama_template/template_manage.php)
- [添加模板](http://mpr.hkzhenshang.com/ama_template/template_add.php)
- [导入模板](http://mpr.hkzhenshang.com/ama_template/Import_text.php)

其它

- [打印工作日志](http://mpr.hkzhenshang.com/work.php)
- [生成货架编号](http://mpr.hkzhenshang.com/Shelf.php)
- [新版架货](http://mpr.hkzhenshang.com/Positions.php)
- [记事本](http://mpr.hkzhenshang.com/news_list.php)
- [飞机盒调货](http://mpr.hkzhenshang.com/Transfer_goods.php)
- [不能用的词](http://mpr.hkzhenshang.com/Unusable_words/add_word.php)
- [重新上传SKU的后缀](http://mpr.hkzhenshang.com/re_sku_suffix.php)

订单测试

- [获取订单信息](http://mpr.hkzhenshang.com/get_order/select_user_order.php)

[修改密码](http://mpr.hkzhenshang.com/pwd_edit.php)