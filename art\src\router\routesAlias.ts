/**
 * 路由别名，方便快速找到页面，同时可以用作路由跳转
 */
export enum RoutesAlias {
  Layout = '/index/index', // 布局容器
  Login = '/auth/login', // 登录
  Register = '/auth/register', // 注册
  ForgetPassword = '/auth/forget-password', // 忘记密码
  Exception403 = '/exception/403', // 403
  Exception404 = '/exception/404', // 404
  Exception500 = '/exception/500', // 500
  Success = '/result/success', // 成功
  Fail = '/result/fail', // 失败
  Dashboard = '/dashboard/console/index', // 工作台
  Analysis = '/dashboard/analysis/index', // 分析页
  Ecommerce = '/dashboard/ecommerce/index', // 电子商务
  DashboardTest = '/dashboard/test/index', // 系统测试
  IconList = '/widgets/icon-list/index', // 图标列表
  IconSelector = '/widgets/icon-selector/index', // 图标选择器
  ImageCrop = '/widgets/image-crop/index', // 图片裁剪
  Excel = '/widgets/excel/index', // Excel
  Video = '/widgets/video/index', // 视频
  CountTo = '/widgets/count-to/index', // 计数
  WangEditor = '/widgets/wang-editor/index', // 富文本编辑器
  Watermark = '/widgets/watermark/index', // 水印
  ContextMenu = '/widgets/context-menu/index', // 上下文菜单
  Qrcode = '/widgets/qrcode/index', // 二维码
  Drag = '/widgets/drag/index', // 拖拽
  TextScroll = '/widgets/text-scroll/index', // 文字滚动
  Fireworks = '/widgets/fireworks/index', // 礼花效果
  Chat = '/template/chat/index', // 聊天
  Cards = '/template/cards/index', // 卡片
  Banners = '/template/banners/index', // 横幅
  Charts = '/template/charts/index', // 图表
  Map = '/template/map/index', // 地图
  Calendar = '/template/calendar/index', // 日历
  Pricing = '/template/pricing/index', // 定价
  ArticleList = '/article/list/index', // 文章列表
  ArticleDetail = '/article/detail/index', // 文章详情
  Comment = '/article/comment/index', // 评论
  ArticlePublish = '/article/publish/index', // 文章发布
  User = '/system/user/index', // 账户
  Role = '/system/role/index', // 角色
  UserCenter = '/system/user-center/index', // 用户中心
  Menu = '/system/menu/index', // 菜单
  BusinessCustomer = '/business/customer/index', // 客户管理
  BusinessProduct = '/business/product/index', // 产品管理
  NestedMenu1 = '/system/nested/menu1/index', // 嵌套菜单1
  NestedMenu21 = '/system/nested/menu2/index', // 嵌套菜单2-1
  NestedMenu31 = '/system/nested/menu3/index', // 嵌套菜单3-1
  NestedMenu321 = '/system/nested/menu3/menu3-2/index', // 嵌套菜单3-2-1
  Server = '/safeguard/server/index', // 服务器
  ChangeLog = '/change/log/index', // 更新日志
  ExamplesTabs = '/examples/tabs/index', // 标签页
  ExamplesTablesBasic = '/examples/tables/basic', // 基础表格示例
  ExamplesTables = '/examples/tables/index' // 高级表格示例
}
