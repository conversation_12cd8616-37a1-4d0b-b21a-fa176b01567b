import request from '@/utils/http'

/**
 * 业务模块API服务
 * 包含客户、产品、订单等业务数据的API调用
 */

// 客户管理API
export class CustomerService {
  // 获取客户列表
  static getCustomerList(params: Api.Common.PaginatingSearchParams) {
    return request.get<Api.Business.CustomerListData>({
      url: '/api/v1/business/customers/',
      params
    })
  }

  // 创建客户
  static createCustomer(params: Api.Business.CreateCustomerParams) {
    return request.post<Api.Business.CreateCustomerResponse>({
      url: '/api/v1/business/customers/',
      params
    })
  }

  // 获取客户详情
  static getCustomerDetail(id: number) {
    return request.get<Api.Business.CustomerDetailResponse>({
      url: `/api/v1/business/customers/${id}/`
    })
  }

  // 更新客户
  static updateCustomer(id: number, params: Api.Business.UpdateCustomerParams) {
    return request.put<Api.Business.UpdateCustomerResponse>({
      url: `/api/v1/business/customers/${id}/`,
      params
    })
  }

  // 删除客户
  static deleteCustomer(id: number) {
    return request.del<Api.Business.DeleteCustomerResponse>({
      url: `/api/v1/business/customers/${id}/`
    })
  }
}

// 产品管理API
export class ProductService {
  // 获取产品列表
  static getProductList(params: Api.Common.PaginatingSearchParams) {
    return request.get<Api.Business.ProductListData>({
      url: '/api/v1/business/products/',
      params
    })
  }

  // 创建产品
  static createProduct(params: Api.Business.CreateProductParams) {
    return request.post<Api.Business.CreateProductResponse>({
      url: '/api/v1/business/products/',
      params
    })
  }

  // 获取产品详情
  static getProductDetail(id: number) {
    return request.get<Api.Business.ProductDetailResponse>({
      url: `/api/v1/business/products/${id}/`
    })
  }

  // 更新产品
  static updateProduct(id: number, params: Api.Business.UpdateProductParams) {
    return request.put<Api.Business.UpdateProductResponse>({
      url: `/api/v1/business/products/${id}/`,
      params
    })
  }

  // 删除产品
  static deleteProduct(id: number) {
    return request.del<Api.Business.DeleteProductResponse>({
      url: `/api/v1/business/products/${id}/`
    })
  }
}

// 测试数据API
export class TestDataService {
  // 创建测试数据
  static createTestData() {
    return request.post<Api.Business.CreateTestDataResponse>({
      url: '/api/v1/business/test-data/create/'
    })
  }

  // 获取区域数据概览
  static getRegionDataOverview() {
    return request.get<Api.Business.RegionDataOverviewResponse>({
      url: '/api/v1/business/test-data/list/'
    })
  }

  // 获取数据库状态
  static getDatabaseStatus() {
    return request.get<Api.Business.DatabaseStatusResponse>({
      url: '/api/v1/business/database/status/'
    })
  }
}
