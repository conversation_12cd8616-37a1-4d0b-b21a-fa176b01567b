/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArtBackToTop: typeof import('./../components/core/base/art-back-to-top/index.vue')['default']
    ArtBarChart: typeof import('./../components/core/charts/art-bar-chart/index.vue')['default']
    ArtBarChartCard: typeof import('./../components/core/cards/art-bar-chart-card/index.vue')['default']
    ArtBasicBanner: typeof import('./../components/core/banners/art-basic-banner/index.vue')['default']
    ArtBreadcrumb: typeof import('./../components/core/layouts/art-breadcrumb/index.vue')['default']
    ArtButtonMore: typeof import('./../components/core/forms/art-button-more/index.vue')['default']
    ArtButtonTable: typeof import('./../components/core/forms/art-button-table/index.vue')['default']
    ArtCardBanner: typeof import('./../components/core/banners/art-card-banner/index.vue')['default']
    ArtChartEmpty: typeof import('./../components/core/base/art-chart-empty/index.vue')['default']
    ArtChatWindow: typeof import('./../components/core/layouts/art-chat-window/index.vue')['default']
    ArtCountTo: typeof import('./../components/core/text-effect/art-count-to/index.vue')['default']
    ArtCutterImg: typeof import('./../components/core/media/art-cutter-img/index.vue')['default']
    ArtDataListCard: typeof import('./../components/core/cards/art-data-list-card/index.vue')['default']
    ArtDonutChartCard: typeof import('./../components/core/cards/art-donut-chart-card/index.vue')['default']
    ArtDragVerify: typeof import('./../components/core/forms/art-drag-verify/index.vue')['default']
    ArtDualBarCompareChart: typeof import('./../components/core/charts/art-dual-bar-compare-chart/index.vue')['default']
    ArtExcelExport: typeof import('./../components/core/forms/art-excel-export/index.vue')['default']
    ArtExcelImport: typeof import('./../components/core/forms/art-excel-import/index.vue')['default']
    ArtException: typeof import('./../components/core/views/exception/ArtException.vue')['default']
    ArtFastEnter: typeof import('./../components/core/layouts/art-fast-enter/index.vue')['default']
    ArtFestivalTextScroll: typeof import('./../components/core/text-effect/art-festival-text-scroll/index.vue')['default']
    ArtFireworksEffect: typeof import('./../components/core/layouts/art-fireworks-effect/index.vue')['default']
    ArtGlobalSearch: typeof import('./../components/core/layouts/art-global-search/index.vue')['default']
    ArtHBarChart: typeof import('./../components/core/charts/art-h-bar-chart/index.vue')['default']
    ArtHeaderBar: typeof import('./../components/core/layouts/art-header-bar/index.vue')['default']
    ArtHorizontalMenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/index.vue')['default']
    ArtIconSelector: typeof import('./../components/core/base/art-icon-selector/index.vue')['default']
    ArtImageCard: typeof import('./../components/core/cards/art-image-card/index.vue')['default']
    ArtKLineChart: typeof import('./../components/core/charts/art-k-line-chart/index.vue')['default']
    ArtLayouts: typeof import('./../components/core/layouts/art-layouts/index.vue')['default']
    ArtLineChart: typeof import('./../components/core/charts/art-line-chart/index.vue')['default']
    ArtLineChartCard: typeof import('./../components/core/cards/art-line-chart-card/index.vue')['default']
    ArtLogo: typeof import('./../components/core/base/art-logo/index.vue')['default']
    ArtMapChart: typeof import('./../components/core/charts/art-map-chart/index.vue')['default']
    ArtMenuRight: typeof import('./../components/core/others/art-menu-right/index.vue')['default']
    ArtMixedMenu: typeof import('./../components/core/layouts/art-menus/art-mixed-menu/index.vue')['default']
    ArtNotification: typeof import('./../components/core/layouts/art-notification/index.vue')['default']
    ArtPageContent: typeof import('./../components/core/layouts/art-page-content/index.vue')['default']
    ArtProgressCard: typeof import('./../components/core/cards/art-progress-card/index.vue')['default']
    ArtRadarChart: typeof import('./../components/core/charts/art-radar-chart/index.vue')['default']
    ArtResultPage: typeof import('./../components/core/views/result/ArtResultPage.vue')['default']
    ArtRingChart: typeof import('./../components/core/charts/art-ring-chart/index.vue')['default']
    ArtScatterChart: typeof import('./../components/core/charts/art-scatter-chart/index.vue')['default']
    ArtScreenLock: typeof import('./../components/core/layouts/art-screen-lock/index.vue')['default']
    ArtSearchBar: typeof import('./../components/core/forms/art-search-bar/index.vue')['default']
    ArtSearchDate: typeof import('./../components/core/forms/art-search-bar/widget/art-search-date/index.vue')['default']
    ArtSearchInput: typeof import('./../components/core/forms/art-search-bar/widget/art-search-input/index.vue')['default']
    ArtSearchRadio: typeof import('./../components/core/forms/art-search-bar/widget/art-search-radio/index.vue')['default']
    ArtSearchSelect: typeof import('./../components/core/forms/art-search-bar/widget/art-search-select/index.vue')['default']
    ArtSettingsPanel: typeof import('./../components/core/layouts/art-settings-panel/index.vue')['default']
    ArtSidebarMenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/index.vue')['default']
    ArtStatsCard: typeof import('./../components/core/cards/art-stats-card/index.vue')['default']
    ArtTable: typeof import('./../components/core/tables/art-table/index.vue')['default']
    ArtTableHeader: typeof import('./../components/core/tables/art-table-header/index.vue')['default']
    ArtTextScroll: typeof import('./../components/core/text-effect/art-text-scroll/index.vue')['default']
    ArtTimelineListCard: typeof import('./../components/core/cards/art-timeline-list-card/index.vue')['default']
    ArtVideoPlayer: typeof import('./../components/core/media/art-video-player/index.vue')['default']
    ArtWangEditor: typeof import('./../components/core/forms/art-wang-editor/index.vue')['default']
    ArtWatermark: typeof import('./../components/core/others/art-watermark/index.vue')['default']
    ArtWorkTab: typeof import('./../components/core/layouts/art-work-tab/index.vue')['default']
    BasicSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/BasicSettings.vue')['default']
    BoxStyleSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/BoxStyleSettings.vue')['default']
    ColorSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/ColorSettings.vue')['default']
    CommentItem: typeof import('./../components/custom/comment-widget/widget/CommentItem.vue')['default']
    CommentWidget: typeof import('./../components/custom/comment-widget/index.vue')['default']
    ContainerSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/ContainerSettings.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    HorizontalSubmenu: typeof import('./../components/core/layouts/art-menus/art-horizontal-menu/widget/HorizontalSubmenu.vue')['default']
    LoginLeftView: typeof import('./../components/core/views/login/LoginLeftView.vue')['default']
    MenuLayoutSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/MenuLayoutSettings.vue')['default']
    MenuStyleSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/MenuStyleSettings.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SectionTitle: typeof import('./../components/core/layouts/art-settings-panel/widget/SectionTitle.vue')['default']
    SettingDrawer: typeof import('./../components/core/layouts/art-settings-panel/widget/SettingDrawer.vue')['default']
    SettingHeader: typeof import('./../components/core/layouts/art-settings-panel/widget/SettingHeader.vue')['default']
    SettingItem: typeof import('./../components/core/layouts/art-settings-panel/widget/SettingItem.vue')['default']
    SidebarSubmenu: typeof import('./../components/core/layouts/art-menus/art-sidebar-menu/widget/SidebarSubmenu.vue')['default']
    ThemeSettings: typeof import('./../components/core/layouts/art-settings-panel/widget/ThemeSettings.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
