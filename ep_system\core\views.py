from django.shortcuts import render
from django.contrib.auth import authenticate
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import UserRegistrationSerializer
from .models import CustomUser
from .middleware import get_current_region, get_current_domain_info

# Create your views here.

class RegistrationAPIView(generics.CreateAPIView):
    """
    用户注册API视图
    
    支持区域感知的用户注册功能：
    - 自动关联请求头中X-Region-Code对应的区域
    - 默认角色为MEMBER（普通会员）
    - 在当前区域内验证用户名和邮箱的唯一性
    - 不需要认证即可访问
    """
    
    queryset = CustomUser.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]  # 允许未认证用户访问
    
    def create(self, request, *args, **kwargs):
        """处理用户注册请求"""
        
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            
            return Response({
                'success': True,
                'message': '用户注册成功',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': '注册失败，请检查输入信息',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def get_serializer_context(self):
        """为序列化器提供上下文信息"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class LoginAPIView(APIView):
    """
    用户登录API视图

    接收用户名和密码，返回JWT token
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """处理分区域登录请求"""
        username = request.data.get('userName')  # 前端发送的是userName
        password = request.data.get('password')

        if not username or not password:
            return Response({
                'code': 400,
                'msg': '用户名和密码不能为空',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 直接查询用户，绕过区域限制，使用unfiltered查询
        try:
            # 使用unfiltered()方法绕过RegionAwareManager的过滤
            user = CustomUser.objects.unfiltered().get(username=username)

            if user.check_password(password):
                # 生成JWT token
                refresh = RefreshToken.for_user(user)

                # 构建用户信息
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'roleDisplay': user.get_role_display(),
                }

                # 添加区域信息
                if user.region:
                    user_data.update({
                        'regionId': user.region.id,
                        'regionName': user.region.name,
                        'regionCode': user.region.code,
                    })
                else:
                    # 超级管理员没有区域限制
                    user_data.update({
                        'regionId': None,
                        'regionName': '全局管理',
                        'regionCode': 'SUPER',
                    })

                return Response({
                    'code': 200,
                    'msg': '登录成功',
                    'data': {
                        'token': f'Bearer {str(refresh.access_token)}',
                        'refreshToken': str(refresh),
                        'userInfo': user_data
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'code': 401,
                    'msg': '用户名或密码错误',
                    'data': None
                }, status=status.HTTP_401_UNAUTHORIZED)
        except CustomUser.DoesNotExist:
            return Response({
                'code': 401,
                'msg': '用户名或密码错误',
                'data': None
            }, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': '服务器内部错误',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MenuAPIView(APIView):
    """
    动态菜单API视图
    根据用户角色返回对应的菜单配置
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户菜单"""
        user = request.user

        # 基础菜单结构
        base_menus = [
            {
                'name': 'Dashboard',
                'path': '/dashboard',
                'component': '/index/index',
                'meta': {
                    'title': 'menus.dashboard.title',
                    'icon': '&#xe721;',
                    'roles': ['R_SUPER', 'R_ADMIN', 'R_USER']
                },
                'children': [
                    {
                        'name': 'DashboardAnalysis',
                        'path': '/dashboard/analysis',
                        'component': '/dashboard/analysis/index',
                        'meta': {
                            'title': 'menus.dashboard.analysis',
                            'icon': '&#xe6a3;'
                        }
                    },
                    {
                        'name': 'DashboardWorkbench',
                        'path': '/dashboard/workbench',
                        'component': '/dashboard/workbench/index',
                        'meta': {
                            'title': 'menus.dashboard.workbench',
                            'icon': '&#xe6a4;'
                        }
                    }
                ]
            }
        ]

        # 根据用户角色添加系统管理菜单
        if user.role in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            system_menu = {
                'name': 'System',
                'path': '/system',
                'component': '/index/index',
                'meta': {
                    'title': 'menus.system.title',
                    'icon': '&#xe6a5;',
                    'roles': ['R_SUPER', 'R_ADMIN']
                },
                'children': [
                    {
                        'name': 'SystemUser',
                        'path': '/system/user',
                        'component': '/system/user/index',
                        'meta': {
                            'title': 'menus.system.user',
                            'icon': '&#xe6a6;'
                        }
                    }
                ]
            }
            base_menus.append(system_menu)

        # 添加业务管理菜单
        business_menu = {
            'name': 'Business',
            'path': '/business',
            'component': '/index/index',
            'meta': {
                'title': 'menus.business.title',
                'icon': '&#xe6a7;',
                'roles': ['R_SUPER', 'R_ADMIN', 'R_USER']
            },
            'children': [
                {
                    'name': 'BusinessCustomer',
                    'path': '/business/customer',
                    'component': '/business/customer/index',
                    'meta': {
                        'title': 'menus.business.customer',
                        'icon': '&#xe6a8;'
                    }
                },
                {
                    'name': 'BusinessProduct',
                    'path': '/business/product',
                    'component': '/business/product/index',
                    'meta': {
                        'title': 'menus.business.product',
                        'icon': '&#xe6a9;'
                    }
                }
            ]
        }
        base_menus.append(business_menu)

        return Response({
            'code': 200,
            'msg': '获取菜单成功',
            'data': {
                'menuList': base_menus
            }
        }, status=status.HTTP_200_OK)


class UserInfoAPIView(APIView):
    """
    获取当前用户信息API视图
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取当前登录用户的信息"""
        user = request.user

        # 构建用户信息
        user_data = {
            'userId': user.id,
            'userName': user.username,
            'email': user.email,
            'phone': getattr(user, 'phone', ''),
            'role': user.role,
            'roleDisplay': user.get_role_display(),
            'avatar': '',
            'roles': [user.role.lower()],  # 根据实际角色设置
            'buttons': ['add', 'edit', 'delete'],  # 简化处理
        }

        # 添加区域信息
        if user.region:
            user_data.update({
                'regionId': user.region.id,
                'regionName': user.region.name,
                'regionCode': user.region.code,
            })
        else:
            # 超级管理员没有区域限制
            user_data.update({
                'regionId': None,
                'regionName': '全局管理',
                'regionCode': 'SUPER',
            })

        return Response({
            'code': 200,
            'msg': '获取用户信息成功',
            'data': user_data
        }, status=status.HTTP_200_OK)


class UserListAPIView(APIView):
    """
    获取用户列表API视图
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户列表 - 支持区域感知"""
        current = int(request.GET.get('current', 1))
        size = int(request.GET.get('size', 10))

        # 计算分页
        start = (current - 1) * size
        end = start + size

        # 根据当前用户角色决定查询范围
        current_user = request.user
        if current_user.role == CustomUser.Role.SUPER_ADMIN:
            # 超级管理员可以看到所有活跃用户
            users = CustomUser.objects.unfiltered().filter(is_active=True)[start:end]
            total = CustomUser.objects.unfiltered().filter(is_active=True).count()
        else:
            # 区域管理员只能看到同区域的活跃用户
            users = CustomUser.objects.filter(region=current_user.region, is_active=True)[start:end]
            total = CustomUser.objects.filter(region=current_user.region, is_active=True).count()

        user_list = []
        for user in users:
            user_data = {
                'id': user.id,
                'avatar': '',
                'createBy': 'system',
                'createTime': user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                'updateBy': 'system',
                'updateTime': user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                'status': '1',  # 在线状态
                'userName': user.username,
                'userGender': '未知',
                'nickName': user.username,
                'userPhone': getattr(user, 'phone', ''),
                'userEmail': user.email,
                'userRoles': [user.role.lower()],
                'role': user.role,
                'roleDisplay': user.get_role_display(),
            }

            # 添加区域信息
            if user.region:
                user_data.update({
                    'regionId': user.region.id,
                    'regionName': user.region.name,
                    'regionCode': user.region.code,
                })
            else:
                user_data.update({
                    'regionId': None,
                    'regionName': '全局管理',
                    'regionCode': 'SUPER',
                })

            # 添加会员助理信息
            if user.role == CustomUser.Role.MEMBER and user.managed_by:
                user_data.update({
                    'managedById': user.managed_by.id,
                    'managedByName': user.managed_by.username,
                    'managedByEmail': user.managed_by.email,
                })
            else:
                user_data.update({
                    'managedById': None,
                    'managedByName': None,
                    'managedByEmail': None,
                })

            user_list.append(user_data)

        return Response({
            'code': 200,
            'msg': '获取用户列表成功',
            'data': {
                'records': user_list,
                'current': current,
                'size': size,
                'total': total
            }
        }, status=status.HTTP_200_OK)

    def post(self, request):
        """管理员创建用户"""
        current_user = request.user

        # 权限检查：只有管理员可以创建用户
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以创建用户',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        # 获取请求数据
        username = request.data.get('username')
        email = request.data.get('email')
        phone = request.data.get('phone', '')
        role = request.data.get('role', CustomUser.Role.MEMBER)
        password = request.data.get('password', 'temp123456')  # 默认密码
        managed_by_id = request.data.get('managed_by_id')  # 负责助理ID（仅对MEMBER有效）
        permission_ids = request.data.get('permission_ids', [])  # 权限ID列表
        responsible_member_ids = request.data.get('responsible_member_ids', [])  # 助理负责的会员ID列表

        # 数据验证
        if not username or not email:
            return Response({
                'code': 400,
                'msg': '用户名和邮箱不能为空',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查用户名是否已存在
        if CustomUser.objects.unfiltered().filter(username=username).exists():
            return Response({
                'code': 400,
                'msg': '用户名已存在',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查邮箱是否已存在
        if CustomUser.objects.unfiltered().filter(email=email).exists():
            return Response({
                'code': 400,
                'msg': '邮箱已存在',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 确定用户区域
        if current_user.role == CustomUser.Role.SUPER_ADMIN:
            # 超级管理员可以指定区域，默认为当前区域
            from .middleware import get_current_region
            region = get_current_region() or current_user.region
        else:
            # 区域管理员只能在自己的区域创建用户
            region = current_user.region

        # 处理会员助理关联
        managed_by = None
        if role == CustomUser.Role.MEMBER and managed_by_id:
            try:
                # 验证助理是否存在且为MEMBER_ASSISTANT角色
                managed_by = CustomUser.objects.unfiltered().get(
                    id=managed_by_id,
                    role=CustomUser.Role.MEMBER_ASSISTANT,
                    region=region  # 确保助理和会员在同一区域
                )
            except CustomUser.DoesNotExist:
                return Response({
                    'code': 400,
                    'msg': '指定的助理不存在或不是会员助理角色',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 创建用户
            user = CustomUser.objects.create_user(
                username=username,
                email=email,
                password=password,
                phone=phone,
                role=role,
                region=region,
                managed_by=managed_by
            )

            # 处理权限分配
            permissions_assigned = 0
            if permission_ids:
                from django.db import connection
                for perm_id in permission_ids:
                    try:
                        with connection.cursor() as cursor:
                            cursor.execute("""
                                INSERT INTO core_userpermission (user_id, permission_id, granted_by_id)
                                VALUES (%s, %s, %s)
                            """, [user.id, perm_id, current_user.id])
                            permissions_assigned += 1
                    except Exception as e:
                        print(f"权限分配失败: {e}")

            # 处理助理负责会员的关联（如果是助理角色）
            members_assigned = 0
            if role == CustomUser.Role.MEMBER_ASSISTANT and responsible_member_ids:
                try:
                    # 将指定的会员设置为由该助理负责
                    members_to_update = CustomUser.objects.filter(
                        id__in=responsible_member_ids,
                        role=CustomUser.Role.MEMBER,
                        region=user.region  # 确保在同一区域
                    )
                    members_assigned = members_to_update.update(managed_by=user)
                except Exception as e:
                    print(f"助理-会员关联失败: {e}")

            return Response({
                'code': 200,
                'msg': '用户创建成功',
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'phone': user.phone,
                    'role': user.role,
                    'roleDisplay': user.get_role_display(),
                    'regionName': region.name if region else '全局管理',
                    'defaultPassword': password,
                    'permissions_assigned': permissions_assigned,
                    'members_assigned': members_assigned
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'创建用户失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AssistantListAPIView(APIView):
    """获取会员助理列表API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取当前区域的会员助理列表"""
        current_user = request.user

        # 权限检查：只有管理员可以查看助理列表
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 获取当前区域的会员助理
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                # 超级管理员可以看到所有活跃助理
                assistants = CustomUser.objects.unfiltered().filter(
                    role=CustomUser.Role.MEMBER_ASSISTANT,
                    is_active=True
                )
            else:
                # 区域管理员只能看到同区域的活跃助理
                assistants = CustomUser.objects.filter(
                    role=CustomUser.Role.MEMBER_ASSISTANT,
                    region=current_user.region,
                    is_active=True
                )

            assistant_list = []
            for assistant in assistants:
                assistant_data = {
                    'id': assistant.id,
                    'username': assistant.username,
                    'email': assistant.email,
                    'phone': getattr(assistant, 'phone', ''),
                    'regionName': assistant.region.name if assistant.region else '全局',
                    'managedMemberCount': assistant.managed_members.count(),  # 负责的会员数量
                }
                assistant_list.append(assistant_data)

            return Response({
                'code': 200,
                'msg': '获取助理列表成功',
                'data': assistant_list
            })

        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取助理列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DomainInfoAPIView(APIView):
    """
    域名信息API视图
    用于测试多域名支持功能
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """获取当前请求的域名和区域信息"""

        # 获取域名信息
        domain_info = get_current_domain_info()
        current_region = get_current_region()

        # 构建响应数据
        response_data = {
            'request_info': {
                'host': request.get_host(),
                'method': request.method,
                'path': request.path,
                'headers': {
                    'user-agent': request.headers.get('User-Agent', ''),
                    'x-region-code': request.headers.get('X-Region-Code', ''),
                }
            },
            'domain_info': domain_info,
            'region_info': {
                'current_region': {
                    'id': current_region.id if current_region else None,
                    'name': current_region.name if current_region else None,
                    'code': current_region.code if current_region else None,
                } if current_region else None,
                'access_type': domain_info.get('access_type') if domain_info else 'unknown'
            }
        }

        return Response({
            'code': 200,
            'msg': '获取域名信息成功',
            'data': response_data
        }, status=status.HTTP_200_OK)


class UserDetailAPIView(APIView):
    """用户详情API - 支持删除、更新等操作"""
    permission_classes = [IsAuthenticated]

    def get(self, request, user_id):
        """获取用户详情"""
        current_user = request.user

        try:
            # 查找用户
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                user = CustomUser.objects.unfiltered().get(id=user_id)
            else:
                user = CustomUser.objects.get(id=user_id, region=current_user.region)

            # 获取用户权限
            user_permissions = []
            if user.role != CustomUser.Role.SUPER_ADMIN:
                from django.db import connection
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT p.id, p.name, p.code, p.description, p.category
                        FROM core_permission p
                        INNER JOIN core_userpermission up ON p.id = up.permission_id
                        WHERE up.user_id = %s
                    """, [user.id])

                    for row in cursor.fetchall():
                        user_permissions.append({
                            'id': row[0],
                            'name': row[1],
                            'code': row[2],
                            'description': row[3],
                            'category': row[4]
                        })

            # 获取负责的会员（如果是助理）
            responsible_members = []
            if user.role == CustomUser.Role.MEMBER_ASSISTANT:
                managed_members = CustomUser.objects.filter(managed_by=user)
                responsible_members = [
                    {
                        'id': member.id,
                        'username': member.username,
                        'email': member.email
                    }
                    for member in managed_members
                ]

            return Response({
                'code': 200,
                'msg': '获取用户详情成功',
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'phone': user.phone,
                    'role': user.role,
                    'roleDisplay': user.get_role_display(),
                    'regionName': user.region.name if user.region else None,
                    'managedBy': {
                        'id': user.managed_by.id,
                        'username': user.managed_by.username,
                        'email': user.managed_by.email
                    } if user.managed_by else None,
                    'permissions': user_permissions,
                    'responsibleMembers': responsible_members
                }
            })

        except CustomUser.DoesNotExist:
            return Response({
                'code': 404,
                'msg': '用户不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取用户详情失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, user_id):
        """更新用户信息"""
        current_user = request.user
        data = request.data

        # 权限检查
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以编辑用户',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 查找要更新的用户
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                user = CustomUser.objects.unfiltered().get(id=user_id)
            else:
                user = CustomUser.objects.get(id=user_id, region=current_user.region)

            # 更新基本信息
            user.username = data.get('username', user.username)
            user.email = data.get('email', user.email)
            user.phone = data.get('phone', user.phone)

            # 角色变更处理
            new_role = data.get('role')
            if new_role and new_role != user.role:
                user.role = new_role
                # 角色变更时清理相关关联
                if new_role != CustomUser.Role.MEMBER:
                    user.managed_by = None
                if new_role != CustomUser.Role.MEMBER_ASSISTANT:
                    # 清理助理负责的会员
                    CustomUser.objects.filter(managed_by=user).update(managed_by=None)

            user.save()

            # 处理权限更新
            permission_ids = data.get('permission_ids', [])
            if user.role != CustomUser.Role.SUPER_ADMIN:
                from django.db import connection

                # 删除现有权限
                with connection.cursor() as cursor:
                    cursor.execute("DELETE FROM core_userpermission WHERE user_id = %s", [user.id])

                # 添加新权限
                permissions_assigned = 0
                for perm_id in permission_ids:
                    try:
                        with connection.cursor() as cursor:
                            cursor.execute("""
                                INSERT INTO core_userpermission (user_id, permission_id, granted_by_id)
                                VALUES (%s, %s, %s)
                            """, [user.id, perm_id, current_user.id])
                            permissions_assigned += 1
                    except Exception as e:
                        print(f"权限分配失败 {perm_id}: {e}")

            # 处理助理-会员关联更新
            responsible_member_ids = data.get('responsible_member_ids', [])
            if user.role == CustomUser.Role.MEMBER_ASSISTANT:
                # 先清理现有关联
                CustomUser.objects.filter(managed_by=user).update(managed_by=None)

                # 建立新关联
                members_assigned = 0
                for member_id in responsible_member_ids:
                    try:
                        member = CustomUser.objects.get(id=member_id, role=CustomUser.Role.MEMBER)
                        member.managed_by = user
                        member.save()
                        members_assigned += 1
                    except CustomUser.DoesNotExist:
                        print(f"会员不存在: {member_id}")

            return Response({
                'code': 200,
                'msg': '用户更新成功',
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'roleDisplay': user.get_role_display(),
                    'permissions_assigned': permissions_assigned if user.role != CustomUser.Role.SUPER_ADMIN else 'all',
                    'members_assigned': members_assigned if user.role == CustomUser.Role.MEMBER_ASSISTANT else 0
                }
            })

        except CustomUser.DoesNotExist:
            return Response({
                'code': 404,
                'msg': '用户不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'更新用户失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, user_id):
        """删除用户"""
        current_user = request.user

        # 权限检查：只有管理员可以删除用户
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以删除用户',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 查找要删除的用户
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                # 超级管理员可以删除任何用户
                user_to_delete = CustomUser.objects.unfiltered().get(id=user_id)
            else:
                # 区域管理员只能删除同区域的用户
                user_to_delete = CustomUser.objects.get(id=user_id, region=current_user.region)

            # 不能删除自己
            if user_to_delete.id == current_user.id:
                return Response({
                    'code': 400,
                    'msg': '不能删除自己',
                    'data': None
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查是否有关联数据需要处理
            if user_to_delete.role == CustomUser.Role.MEMBER_ASSISTANT:
                # 如果删除的是助理，需要处理其负责的会员
                managed_members = CustomUser.objects.filter(managed_by=user_to_delete)
                if managed_members.exists():
                    # 将这些会员的助理关联设为空
                    managed_members.update(managed_by=None)

            # 软删除：设置为非活跃状态
            user_to_delete.is_active = False
            user_to_delete.save()

            return Response({
                'code': 200,
                'msg': '用户删除成功',
                'data': None
            })

        except CustomUser.DoesNotExist:
            return Response({
                'code': 404,
                'msg': '用户不存在',
                'data': None
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'删除用户失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PermissionListAPIView(APIView):
    """权限列表API"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取权限列表"""
        current_user = request.user

        # 只有管理员可以查看权限列表
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以查看权限列表',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 直接查询权限表
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT id, name, code, description, category
                    FROM core_permission
                    ORDER BY category, name
                """)
                permissions = []
                for row in cursor.fetchall():
                    permissions.append({
                        'id': row[0],
                        'name': row[1],
                        'code': row[2],
                        'description': row[3],
                        'category': row[4]
                    })

            # 按分类分组
            categories = {}
            for perm in permissions:
                category = perm['category']
                if category not in categories:
                    categories[category] = []
                categories[category].append(perm)

            return Response({
                'code': 200,
                'msg': '权限列表获取成功',
                'data': {
                    'permissions': permissions,
                    'categories': categories
                }
            })

        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取权限列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MemberListAPIView(APIView):
    """获取会员列表API（用于助理选择负责的会员）"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取会员列表"""
        current_user = request.user

        # 权限检查：只有管理员可以查看会员列表
        if current_user.role not in [CustomUser.Role.SUPER_ADMIN, CustomUser.Role.REGION_ADMIN]:
            return Response({
                'code': 403,
                'msg': '权限不足，只有管理员可以查看会员列表',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            # 根据当前用户角色决定查询范围
            if current_user.role == CustomUser.Role.SUPER_ADMIN:
                # 超级管理员可以看到所有区域的会员
                members = CustomUser.objects.unfiltered().filter(
                    role=CustomUser.Role.MEMBER,
                    is_active=True
                )
            else:
                # 区域管理员只能看到同区域的会员
                members = CustomUser.objects.filter(
                    role=CustomUser.Role.MEMBER,
                    region=current_user.region,
                    is_active=True
                )

            # 构建响应数据
            member_list = []
            for member in members:
                member_data = {
                    'id': member.id,
                    'username': member.username,
                    'email': member.email,
                    'phone': member.phone,
                    'regionName': member.region.name if member.region else '全局管理',
                    'managed_by': {
                        'id': member.managed_by.id,
                        'username': member.managed_by.username
                    } if member.managed_by else None
                }
                member_list.append(member_data)

            return Response({
                'code': 200,
                'msg': '会员列表获取成功',
                'data': member_list
            })

        except Exception as e:
            return Response({
                'code': 500,
                'msg': f'获取会员列表失败: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
