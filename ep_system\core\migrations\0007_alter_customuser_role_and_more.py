# Generated by Django 5.2.3 on 2025-07-09 07:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_auto_20250708_1601'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.CharField(choices=[('SUPER_ADMIN', '超级管理员'), ('REGION_ADMIN', '区域管理员'), ('MEMBER', '普通会员'), ('MEMBER_ASSISTANT', '会员助理'), ('WAREHOUSE_MANAGER', '库管'), ('TRANSLATOR_ASSISTANT', '翻译助理'), ('DOCUMENT_ASSISTANT', '文档助理'), ('OPERATION_SPECIALIST', '精细化操作人员'), ('FINANCE_STAFF', '财务人员')], default='MEMBER', max_length=20, verbose_name='角色'),
        ),
        migrations.AlterUniqueTogether(
            name='userpermission',
            unique_together={('user', 'permission')},
        ),
        migrations.CreateModel(
            name='AssistantProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, verbose_name='说明(可填写地区等其它信息)')),
                ('related_finance_auth_token', models.CharField(blank=True, max_length=255, verbose_name='关联财务助理AuthToken')),
                ('internal_member_finance_auth_token', models.CharField(blank=True, max_length=255, verbose_name='内部会员财务AuthToken(内部会员的助理才有此项)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(limit_choices_to={'role__in': ['MEMBER_ASSISTANT', 'TRANSLATOR_ASSISTANT', 'DOCUMENT_ASSISTANT']}, on_delete=django.db.models.deletion.CASCADE, related_name='assistant_profile', to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '助理资料',
                'verbose_name_plural': '助理资料',
            },
        ),
        migrations.CreateModel(
            name='MemberProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('english_name', models.CharField(blank=True, max_length=100, verbose_name='英文名(用于FBA发票)')),
                ('description', models.TextField(blank=True, verbose_name='说明(可填写地区等其它信息)')),
                ('finance_auth_token', models.CharField(blank=True, max_length=255, verbose_name='财务AuthToken')),
                ('sku_prefix', models.CharField(blank=True, max_length=50, verbose_name='SKU前缀')),
                ('payment_channel', models.CharField(blank=True, max_length=100, verbose_name='支付渠道')),
                ('parent_account', models.CharField(blank=True, max_length=100, verbose_name='父账号')),
                ('ems_vip_number', models.CharField(blank=True, max_length=100, verbose_name='EMS大客户号')),
                ('ems_customer_code', models.CharField(blank=True, max_length=100, verbose_name='EMS客户代码')),
                ('ems_version_info', models.CharField(blank=True, max_length=100, verbose_name='EMS版本信息')),
                ('international_eub_us', models.CharField(blank=True, max_length=100, verbose_name='international_eub_us_1.1')),
                ('ems_auth_token', models.CharField(blank=True, max_length=255, verbose_name='EMS authToken号')),
                ('amazon_username', models.CharField(blank=True, max_length=100, verbose_name='亚马逊用户名')),
                ('amazon_seller_id', models.CharField(blank=True, max_length=100, verbose_name='亚马逊Seller ID')),
                ('amazon_marketplace_id', models.CharField(blank=True, max_length=100, verbose_name='亚马逊Marketplace ID')),
                ('aws_access_key_id', models.CharField(blank=True, max_length=255, verbose_name='AWS Access Key ID')),
                ('aws_secret_key', models.CharField(blank=True, max_length=255, verbose_name='Secret Key')),
                ('merchant_token', models.CharField(blank=True, max_length=255, verbose_name='Merchant Token')),
                ('developer_aws_access', models.CharField(blank=True, max_length=255, verbose_name='开发者AWS Access')),
                ('developer_secret_key', models.CharField(blank=True, max_length=255, verbose_name='开发者Secret Key')),
                ('mws_auth_token', models.CharField(blank=True, max_length=255, verbose_name='MWS授权令牌')),
                ('warehouse_location', models.CharField(blank=True, max_length=100, verbose_name='产品所在仓库')),
                ('status', models.CharField(default='active', max_length=50, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(limit_choices_to={'role': 'MEMBER'}, on_delete=django.db.models.deletion.CASCADE, related_name='member_profile', to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '会员资料',
                'verbose_name_plural': '会员资料',
            },
        ),
    ]
